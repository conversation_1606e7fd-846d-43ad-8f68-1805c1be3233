import { useState, useEffect } from 'react'
import { useTypedSelector } from './redux-hooks'
import { useDispatch } from 'react-redux'
import { Settings } from 'luxon'
import {
  getUserIANATimezone,
  logout,
  getUser,
  getUserSettingStyleProperties,
} from 'duxs/user'
import { useLocation } from 'react-router'
import { getAppStyleProperties } from 'duxs/client-settings-helper'
import { omit } from 'remeda'
import { defaultStyles } from './util-functions/client-default-styles'
import { getDefaultDateTimeLocale } from 'duxs/user-sensitive-selectors'
import {
  getValidatedSearchParams,
  useValidatedSearchParams,
} from './hooks/useValidatedSearchParams'
import { z } from 'zod'
import { useStructuralSharedUrlValue } from './hooks/useStructuralSharedUrlValue'
import { vehicleIdSchema } from 'api/types'
import { onAppMounted, onUrlLoginSearchParamsChange } from 'duxs/sharedActions'
import { getReduxSyncHistory } from 'duxs/providers-sync'
import { select } from 'typed-redux-saga'

let prevStyleAppName: string | null = null

const otpSearchParamsSchema = z.object({
  otp: z.string().min(1),
  account: z.string().min(1),
  ot_vehicle_id: vehicleIdSchema.optional(),
})

const ssoSearchParamsSchema = z.object({
  sso: z.string().min(1),
  t: z.string().min(1),
})

// For SPF/TFMS federated login
const federatedWResultSchema = z.object({
  wresult: z.string().min(1),
})

const federatedAzureSearchParamsSchema = z.object({
  code: z.string().min(1),
  state: z.string().min(1),
  session_state: z.string().min(1),
})

const urlLoginSearchParamsSchema = otpSearchParamsSchema
  .or(ssoSearchParamsSchema)
  .or(federatedWResultSchema)
  .or(federatedAzureSearchParamsSchema)

/**
 * Used to prevent some sagas polling from running when logging in through url login, e.g: otp or sso.
 * Returns false if user is already logged in, even if login search params are present in URL.
 */
export function* doesUrlContainLoginSearchParams() {
  const history = yield* select(getReduxSyncHistory)
  const validatedParamResult = getValidatedSearchParams({
    locationSearch: history.location.search,
    schema: urlLoginSearchParamsSchema,
  })

  // If no login search params are present, return false
  if (validatedParamResult.status !== 'valid') {
    return false
  }

  // If login search params are present, check if user is already logged in
  // If user is logged in, we should allow polling to proceed (return false)
  // This handles the case where federated login has completed but URL hasn't been cleaned up yet
  const user = yield* select(getUser)
  if (user) {
    return false
  }

  // User is not logged in and login search params are present - block polling
  return true
}

export type ValidUrlLoginSearchParams = z.infer<typeof urlLoginSearchParamsSchema>

export function AppRootEffects({ children }: { children: JSX.Element }) {
  const dispatch = useDispatch()
  const user = useTypedSelector(getUser)
  const { pathname, search } = useLocation()

  const faviconName = useTypedSelector(
    (state) => getAppStyleProperties(state).styleFavicon,
  )

  const stylePropsFromSettings = useTypedSelector(getUserSettingStyleProperties)
  const userIANATimezone = useTypedSelector(getUserIANATimezone)
  const defaultDateTimeLocale = useTypedSelector(getDefaultDateTimeLocale)

  // Weird hacky pattern but the React team recommends it on very specific use cases.
  // https://beta.reactjs.org/learn/you-might-not-need-an-effect#adjusting-some-state-when-a-prop-changes
  // Prevents our app from rendering to the browser once without luxon defaultZone being set.
  const [prevUserIANATimezone, setPrevUserIANATimezone] = useState<
    typeof userIANATimezone | 0
  >(0)

  if (prevUserIANATimezone === 0 || userIANATimezone !== prevUserIANATimezone) {
    // Runs on first render and every time the userIANATimezone changes
    setPrevUserIANATimezone(userIANATimezone)
    if (userIANATimezone) {
      // eslint-disable-next-line react-compiler/react-compiler
      Settings.defaultZone = userIANATimezone
    }
  }

  useEffect(() => {
    dispatch(onAppMounted())
  }, [dispatch])

  // Prevents our app from rendering to the browser once without luxon defaultLocale being correctly set.
  const [prevDefaultDateTimeLocale, setPrevDefaultDateTimeLocale] = useState<
    typeof defaultDateTimeLocale | 0
  >(0)
  if (
    prevDefaultDateTimeLocale === 0 ||
    defaultDateTimeLocale !== prevDefaultDateTimeLocale
  ) {
    setPrevDefaultDateTimeLocale(defaultDateTimeLocale)
    if (defaultDateTimeLocale) {
      Settings.defaultLocale = defaultDateTimeLocale
    }
  }

  useEffect(() => {
    const mainFaviconLink = document.querySelector<HTMLLinkElement>('link[rel="icon"]')
    const appleFaviconLink = document.querySelector<HTMLLinkElement>(
      'link[rel="apple-touch-icon"]',
    )

    if (mainFaviconLink === null || appleFaviconLink === null) {
      throw new Error(
        '[Cartrack] - favicon links must be present on the root .html file',
      )
    }

    const faviconHref = '/assets/' + faviconName

    mainFaviconLink.href = faviconHref
    appleFaviconLink.href = faviconHref
  }, [faviconName])

  useEffect(() => {
    if (user && pathname === '/resetpassword' && search !== '') {
      // A user is logged in and he is trying to reset a user's password through a url. To prevent issues with the process, we log out first
      dispatch(logout({ meta: { preventRedirect: true } }))
    }
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, pathname, search])

  useEffect(() => {
    const stylePropertiesToReturn = {
      ...defaultStyles,
      ...stylePropsFromSettings,
    }

    const stylePropertiesToSet = omit(stylePropertiesToReturn, [
      'styleMainLogoPoweredBy',
      'styleMenuLogoPoweredBy',
      'styleLogoPoweredByType',
      'styleLogoPoweredByType3Label',
      'styleLoginBackgroundColour',
      'karooUiTheme',
      'styleMainLogoWidthPx',
      'styleLoginFooterLogo',
      'styleLoginFooterBackgroundColor',
      'styleLoginFooterHeight',
      'styleLoginForgotUsername',
      'styleLoginLanguage',
      'styleLoginModalFooter',
      'styleLoginSignUp',
      'styleLoginLogoInsideBox',
      'styleLoginStayLoggedIn',
    ])

    for (const _untypedKey in stylePropertiesToSet) {
      const key = _untypedKey as keyof typeof stylePropertiesToSet
      if (key === 'styleAppName') {
        if (
          stylePropertiesToSet.styleAppName &&
          // We use this to prevent this side-effecty selector from overwriting the title when not intended.
          stylePropertiesToSet.styleAppName !== prevStyleAppName
        ) {
          document.title = stylePropertiesToSet.styleAppName
          prevStyleAppName = stylePropertiesToSet.styleAppName
        }
      } else {
        document.documentElement.style.setProperty(
          `--${key}`,
          stylePropertiesToSet[key],
        )
      }
    }
  }, [stylePropsFromSettings])

  const urlLoginSearchParams = useValidatedSearchParams(
    () => urlLoginSearchParamsSchema,
  )
  const parsedUrlLoginSearchParams = useStructuralSharedUrlValue({
    globalUniqueId: 'otpSearchParams',
    value: urlLoginSearchParams.data ?? null,
  })
  useEffect(() => {
    if (parsedUrlLoginSearchParams) {
      dispatch(onUrlLoginSearchParamsChange(parsedUrlLoginSearchParams))
    }
  }, [dispatch, parsedUrlLoginSearchParams])

  return children
}
