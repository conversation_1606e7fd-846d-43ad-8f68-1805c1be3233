import { useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  Drawer,
  GridActionsCellItem,
  GridToolbarExport,
  IconButton,
  LinearProgress,
  MenuItem,
  OverflowTypography,
  Stack,
  TextField,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import EditIcon from '@mui/icons-material/Edit'
import FileUploadIcon from '@mui/icons-material/FileUpload'
import type { Location } from 'history'
import { FormattedMessage } from 'react-intl'
import { useDispatch } from 'react-redux'
import { Link as RouterLink, useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import type { FetchLandmarksResolved } from 'api/landmarks'
import {
  deleteMultipleLandmarks,
  fetchLandmarks,
  getCanLandmarkBeEdited,
  getLandmarks,
  getLoading,
  updateLandmarks,
} from 'duxs/landmarks'
import { getMessages } from 'duxs/locale'
import { getIsUserDistanceInMiles } from 'duxs/user'
import {
  getLandmarksDeleteLandmarkSetting,
  getSettings_UNSAFE,
  getUserPositionAddressStateGetter,
} from 'duxs/user-sensitive-selectors'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import { useModal } from 'src/hooks'
import { getImporterDrawerMainPath } from 'src/modules/app/GlobalDrawers/utils'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'
import { EditPOITabs } from 'src/modules/map-view/FleetMapView/POI/schema'
import { getPOIDetailsModalMainPath } from 'src/modules/map-view/FleetMapView/POI/utils'
import { useTypedSelector } from 'src/redux-hooks'
import { DataGridDeleteButtonWithCounter } from 'src/shared/data-grid/DataGridDeleteButtonWithCounter'
import { DeleteMultipleItemsDialog } from 'src/shared/data-grid/DeleteMultipleItemsDialog'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import IntlTypography from 'src/util-components/IntlTypography'
import { fromMetersToFeet } from 'src/util-functions/distance-utils'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'
import { formatDeleteDialogWithCountText } from 'src/util-functions/translation-utils'

import DocumentIcon from 'assets/svg/Mifleet/Documents.svg'

import { colorOptions, ctIntl } from 'cartrack-ui-kit'

const colorCircle = (hex: string) => (
  <div
    className="util-inlineCircle"
    style={{ background: hex }}
  />
)

type Landmark = FetchLandmarksResolved['landmarks'][number]

function ReLandmarks() {
  const history = useHistory()
  const dispatch = useDispatch()
  const columnHelper = useDataGridColumnHelper<Landmark>({ filterMode: 'client' })
  const landmarksDeletePOI = useTypedSelector(getLandmarksDeleteLandmarkSetting)
  const getUserPositionAddressState = useTypedSelector(
    getUserPositionAddressStateGetter,
  )
  const distanceInMiles = useTypedSelector(getIsUserDistanceInMiles)
  const localeMessages = useTypedSelector(getMessages)
  const { landmarksImportPOI, landmarksEditMultiple, landmarksAddPOI } =
    useTypedSelector(getSettings_UNSAFE)
  const landmarks = useTypedSelector(getLandmarks)
  const loading = useTypedSelector(getLoading)
  const { canLandmarkBeEdited } = useTypedSelector(getCanLandmarkBeEdited)

  const [isEditing, setIsEditing] = useState(false)
  const [selectedColor, setSelectedColor] = useState('')
  const [description, setDescription] = useState('')
  const [rowsIdSelected, setRowsIdSelected] = useState<Array<string>>([])
  const [isDeleteModalOpen, deleteModalContext] = useModal(false)

  const isAnyRowSelected = useMemo(() => rowsIdSelected.length > 0, [rowsIdSelected])

  const hasSelectedLandmarksCannotBeEdited = useMemo(
    () => rowsIdSelected.some((id) => !canLandmarkBeEdited(id)),
    [canLandmarkBeEdited, rowsIdSelected],
  )

  const isEditDetailsButtonDisabled = useMemo(
    () =>
      !isAnyRowSelected || !landmarksEditMultiple || hasSelectedLandmarksCannotBeEdited,
    [isAnyRowSelected, landmarksEditMultiple, hasSelectedLandmarksCannotBeEdited],
  )

  const isDeleteButtonDisabled = useMemo(
    () =>
      !isAnyRowSelected || !landmarksDeletePOI || hasSelectedLandmarksCannotBeEdited,
    [isAnyRowSelected, landmarksDeletePOI, hasSelectedLandmarksCannotBeEdited],
  )

  useEffect(() => {
    dispatch(fetchLandmarks())
  }, [dispatch])

  const clearState = () => {
    // reset "form"
    setSelectedColor('')
    setDescription('')
  }

  const handleSave = () => {
    dispatch(updateLandmarks(rowsIdSelected, selectedColor, description))
    setIsEditing(false)
    clearState()
  }

  const handleCancelButtonClick = () => {
    setIsEditing(false)
    clearState()
  }

  const handleDeleteMultipleLandmarks = () => {
    dispatch(deleteMultipleLandmarks(rowsIdSelected))
    setRowsIdSelected([])
  }

  const columnsGetters = useMemo(
    () =>
      ({
        name: (l: Landmark) => l.name,
        owner: (l: Landmark) => l.owner,
        address: (l: Landmark) =>
          match(getUserPositionAddressState({ address: l.address, gpsFixType: null }))
            .with('EMPTY', () => '')
            .with({ visibility: 'PRIVATE' }, () =>
              ctIntl.formatMessage({ id: 'Privacy Enabled' }),
            )
            .with(
              { visibility: 'PUBLIC' },
              ({ processedDescriptionText }) => processedDescriptionText,
            )
            .exhaustive(),
        description: (l: Landmark) => l.description,
        colorName: (l: Landmark) => l.colorName,
        radius: (l: Landmark) =>
          fromMetersToFeet(l.radius, { distanceInMiles, localeMessages }),
      }) satisfies Record<string, (l: Landmark) => unknown>,
    [getUserPositionAddressState, distanceInMiles, localeMessages],
  )

  const columns = useMemo(
    (): Array<GridColDef<Landmark>> => [
      columnHelper.string((_, row) => columnsGetters.name(row), {
        field: 'name',
        headerName: ctIntl.formatMessage({ id: 'Landmark Name' }),
        maxWidth: 300,
        flex: 1,
      }),
      columnHelper.string((_, row) => columnsGetters.owner(row), {
        field: 'owner',
        headerName: ctIntl.formatMessage({ id: 'Owner' }),
        flex: 1,
        maxWidth: 300,
      }),
      columnHelper.string((_, row) => columnsGetters.address(row), {
        field: 'address',
        headerName: ctIntl.formatMessage({ id: 'Address' }),
        flex: 1,
        renderCell: ({ row }) => (
          <OverflowTypography>
            <UserFormattedPositionAddress
              address={row.address}
              gpsFixType={null}
            />
          </OverflowTypography>
        ),
      }),
      columnHelper.string((_, row) => columnsGetters.description(row), {
        field: 'description',
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => columnsGetters.colorName(row), {
        field: 'colorName',
        headerName: ctIntl.formatMessage({ id: 'Color' }),
        flex: 1,
        maxWidth: 150,
        renderCell: ({ row }) => (
          <span className="util-capitalize">
            {colorCircle(row.color)} <FormattedMessage id={row.colorName} />
          </span>
        ),
      }),
      columnHelper.string((_, row) => columnsGetters.radius(row), {
        field: 'radius',
        headerName: ctIntl.formatMessage({ id: 'Radius' }),
        flex: 1,
        maxWidth: 150,
      }),
      {
        type: 'actions',
        field: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => [
          <Tooltip
            key={row.id}
            title={ctIntl.formatMessage({ id: 'Edit' })}
            arrow
            placement="right"
            disableInteractive // Prevent tooltip from interfering with trying to click the other action cell items
          >
            <span>
              <GridActionsCellItem
                icon={<EditIcon />}
                label={ctIntl.formatMessage({ id: 'Edit' })}
                disabled={!canLandmarkBeEdited(row.id)}
                onClick={() =>
                  history.push(
                    getPOIDetailsModalMainPath(history.location, {
                      poi: 'edit',
                      id: row.id,
                      tab: EditPOITabs.Details,
                    }),
                  )
                }
              />
            </span>
          </Tooltip>,
        ],
      },
    ],
    [canLandmarkBeEdited, columnHelper, columnsGetters, history],
  )

  const handleSelectionModalChange = (selection: GridRowSelectionModel) => {
    setRowsIdSelected(selection as Array<string>)
  }

  return (
    <>
      <PageWithMainTableContainer>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <IntlTypography
            variant="h5"
            msgProps={{ id: 'Points of Interest' }}
          />
          <Stack
            gap={2}
            direction="row"
          >
            <Button
              variant="outlined"
              color="secondary"
              size="small"
              startIcon={<FileUploadIcon />}
              component={RouterLink}
              to={getImporterDrawerMainPath(
                { pathname: window.location.pathname, state: {} } as Location,
                'POI',
              )}
              disabled={!landmarksImportPOI}
            >
              {ctIntl.formatMessage({ id: 'Import Points of Interest' })}
            </Button>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              component={RouterLink}
              to={getPOIDetailsModalMainPath(history.location, {
                poi: 'add',
                initialMapData: 'none',
              })}
              disabled={isEditing || !landmarksAddPOI}
            >
              {ctIntl.formatMessage({ id: 'Add Point of Interest' })}
            </Button>
          </Stack>
        </Box>
        <UserDataGridWithSavedSettingsOnIDB
          checkboxSelection
          disableRowSelectionOnClick
          pagination
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25, page: 0 },
            },
          }}
          rowSelectionModel={rowsIdSelected}
          onRowSelectionModelChange={handleSelectionModalChange}
          Component={DataGrid}
          sx={{
            '.MuiDataGrid-row.Mui-selected': {
              backgroundColor: '#f4773514',
            },
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
          }}
          dataGridId="landmarkList"
          onRowClick={(params) => {
            history.push(
              getPOIDetailsModalMainPath(history.location, {
                poi: 'edit',
                id: params.id as string,
              }),
            )
          }}
          disableVirtualization
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            noRowsOverlay: () => (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  width: '100%',
                }}
              >
                <svg
                  {...makeSanitizedInnerHtmlProp({ dirtyHtml: DocumentIcon })}
                  width={219}
                  style={{ margin: '0 auto' }}
                />
                <Typography sx={{ mt: 2 }}>
                  {ctIntl.formatMessage({ id: 'No data available' })}
                </Typography>
              </Box>
            ),
          }}
          slotProps={{
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                filterButton: { show: true },
                settingsButton: { show: true },
              },
              extraContent: {
                right: (
                  <>
                    <GridToolbarExport
                      variant="outlined"
                      size="small"
                      color="secondary"
                      // TODO: need to enable it when print export stable
                      printOptions={{ disableToolbarButton: true }}
                    />
                    <Tooltip
                      title={
                        hasSelectedLandmarksCannotBeEdited
                          ? ctIntl.formatMessage({
                              id: 'There are points of interest selected that the user does not have permission to edit',
                            })
                          : ''
                      }
                    >
                      <span>
                        <Button
                          variant="outlined"
                          color="secondary"
                          size="small"
                          disabled={isEditDetailsButtonDisabled}
                          onClick={() => setIsEditing(true)}
                        >
                          {ctIntl.formatMessage({ id: 'Edit Details' })}
                        </Button>
                      </span>
                    </Tooltip>
                    <Tooltip
                      title={
                        hasSelectedLandmarksCannotBeEdited
                          ? ctIntl.formatMessage({
                              id: 'There are points of interest selected that the user does not have permission to edit',
                            })
                          : ''
                      }
                    >
                      <span>
                        <DataGridDeleteButtonWithCounter
                          data-testid="LandmarkTable-DeleteMultipleButton"
                          count={rowsIdSelected.length}
                          disabled={isDeleteButtonDisabled}
                          ButtonProps={{
                            size: 'small',
                            onClick: deleteModalContext.open,
                          }}
                        />
                      </span>
                    </Tooltip>
                  </>
                ),
              },
            }),
            pagination: { showFirstButton: true, showLastButton: true },
          }}
          columns={columns}
          rows={landmarks}
          loading={loading}
        />
        {isDeleteModalOpen && (
          <DeleteMultipleItemsDialog
            onClose={deleteModalContext.close}
            title={ctIntl.formatMessage({ id: 'Points of Interest' })}
            contentText={formatDeleteDialogWithCountText({
              count: rowsIdSelected.length,
              categories: {
                one: ctIntl.formatMessage({
                  id: 'Point of Interest',
                }),
                other: ctIntl.formatMessage({
                  id: 'Points of Interest',
                }),
              },
            })}
            ConfirmButtonProps={{
              onClick: () => {
                handleDeleteMultipleLandmarks()
                deleteModalContext.close()
              },
            }}
          />
        )}
      </PageWithMainTableContainer>

      {isEditing && (
        <Drawer
          anchor="right"
          open
          onClose={() => setIsEditing(false)}
          PaperProps={{ sx: { width: 550, maxWidth: '90vw' } }}
        >
          <Stack
            justifyContent="space-between"
            height="100%"
          >
            <Stack
              p={3}
              gap={3}
            >
              <Stack>
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Typography variant="h6">
                    {ctIntl.formatMessage({ id: 'Bulk edit details' })}
                  </Typography>
                  <IconButton onClick={() => setIsEditing(false)}>
                    <CloseIcon />
                  </IconButton>
                </Stack>
                <Typography>
                  {ctIntl.formatMessage(
                    { id: 'list.landmarks.bulkEditMessage' },
                    { values: { count: rowsIdSelected.length } },
                  )}
                </Typography>
              </Stack>
              <Stack gap={2}>
                <TextField
                  select
                  label={ctIntl.formatMessage({ id: 'Color' })}
                  value={selectedColor}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSelectedColor(e.target.value)
                  }
                >
                  {colorOptions.map((color) => (
                    <MenuItem
                      key={color.value}
                      value={color.value}
                    >
                      {/* eslint-disable-next-line sonarjs/new-cap */}
                      {color.Component()}
                    </MenuItem>
                  ))}
                </TextField>
                <TextField
                  label={ctIntl.formatMessage({ id: 'Description' })}
                  value={description}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setDescription(e.target.value)
                  }
                />
              </Stack>
            </Stack>
            <Stack
              sx={{
                borderTop: '1px solid rgba(0, 0, 0, 0.12)',
                flexDirection: 'row',
                justifyContent: 'space-between',
                pt: 2,
                px: 3,
                pb: 3,
              }}
            >
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleCancelButtonClick}
              >
                {ctIntl.formatMessage({ id: 'Cancel' })}
              </Button>
              <Button
                variant="contained"
                type="submit"
                disabled={!selectedColor && !description}
                onClick={handleSave}
              >
                {ctIntl.formatMessage({ id: 'Save' })}
              </Button>
            </Stack>
          </Stack>
        </Drawer>
      )}
    </>
  )
}
export default ReLandmarks
