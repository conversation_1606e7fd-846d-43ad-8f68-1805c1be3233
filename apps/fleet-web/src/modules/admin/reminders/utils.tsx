import { round } from 'lodash'
import type { KarooUiInternalTheme } from '@karoo-ui/core'
import { DateTime, Duration, type DurationUnits } from 'luxon'
import { opacify } from 'polished'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import type { TranslationOrCustomStringMetaObject } from 'api/types'
import type { IntlFormatNumberFn } from 'src/modules/components/connected/useIntl'
import { ctIntl } from 'src/util-components/ctIntl'
import type { FormatMapDistanceFn } from 'src/util-components/map/shared/useMapFormatMeasuredDistance'

import { kilometersToMiles, milesToKilometers } from 'cartrack-utils'
import type { ReminderInfo } from './components/CompleteReminderWizard/types'
import {
  REMINDER_NOT_CONFIGURED,
  type AdminReminderTypes,
  type ReminderCategoryTranslationOrCustomStringMetaObject,
} from './types'

export const getReminderUnitsForFormatNumber = (
  intervalTypeId: AdminReminderTypes.IntervalType['reminder_interval_type_id'] | null,
) =>
  match(intervalTypeId)
    .with(null, () => 'day')
    .with('2', () => 'hour')
    .with('3', () => 'day')
    .with('4', () => 'week')
    .with('5', () => 'month')
    .with('6', () => 'year')
    .otherwise(() => 'day')

export const getReminderUnitsForDateTime = (
  intervalTypeId: AdminReminderTypes.IntervalType['reminder_interval_type_id'] | null,
): DurationUnits =>
  match(intervalTypeId)
    .returnType<DurationUnits>()
    .with(null, () => 'days')
    .with('2', () => 'hours')
    .with('3', () => 'days')
    .with('4', () => 'weeks')
    .with('5', () => 'months')
    .with('6', () => 'years')
    .otherwise(() => 'days')

export const remindersListFormatCellValue = ({
  value,
  criteriaId,
  intervalTypeId,
  formatMapDistance,
  formatNumber,
  dateFormat,
}: {
  value: string | number | null
  criteriaId: AdminReminderTypes.CriteriaType['reminder_criteria_type_id'] | null
  intervalTypeId: AdminReminderTypes.IntervalType['reminder_interval_type_id'] | null
  formatMapDistance: FormatMapDistanceFn
  formatNumber: IntlFormatNumberFn
  dateFormat: 'unit' | 'short'
}) => {
  const criteria = getReminderCriteriaType(criteriaId)

  if (R.isNullish(value)) {
    return value
  }

  return match(criteria)
    .with('DISTANCE', () => formatMapDistance({ valueInMeters: Number(value) }))
    .with('HOS', () =>
      formatNumber(Number(value), {
        style: 'unit',
        unitDisplay: 'long',
        unit: 'hour',
      }),
    )
    .with('DATE', () =>
      match(dateFormat)
        .with('unit', () =>
          formatNumber(Number(value), {
            style: 'unit',
            unitDisplay: 'long',
            unit: getReminderUnitsForFormatNumber(intervalTypeId),
          }),
        )
        .with('short', () => {
          const date = ctIntl.removeServerDateStringTimezone(String(value))

          if (!date) return null

          return DateTime.fromJSDate(new Date(date)).toLocaleString(DateTime.DATE_SHORT)
        })
        .exhaustive(),
    )
    .with(REMINDER_NOT_CONFIGURED, () => REMINDER_NOT_CONFIGURED)
    .exhaustive()
}

export const nextReminderValue = ({
  value,
  reminderInfo,
  formatMapDistance,
  formatNumber,
  settings,
}: {
  value: string | number | null
  reminderInfo: ReminderInfo
  formatMapDistance: FormatMapDistanceFn
  formatNumber: IntlFormatNumberFn
  settings?: { shouldSum?: boolean; timeInterval?: boolean }
}) => {
  const { criteriaTypeId, intervalTypeId, repeatInterval } = reminderInfo

  const criteria = getReminderCriteriaType(criteriaTypeId)

  return match(criteria)
    .with('DISTANCE', () => {
      const mapDistanceValue = settings?.shouldSum
        ? Number(value) + Number(repeatInterval)
        : Number(value)
      return formatMapDistance({ valueInMeters: mapDistanceValue })
    })
    .with('HOS', () => {
      const hours = settings?.shouldSum
        ? Number(value) + Number(repeatInterval)
        : Number(value)
      return formatNumber(hours, {
        style: 'unit',
        unitDisplay: 'long',
        unit: 'hour',
      })
    })
    .with('DATE', () => {
      const today = DateTime.local()
      const reminderDate = value ? DateTime.fromISO(String(value)) : today
      if (settings?.timeInterval) {
        const interval = repeatInterval ? Number(repeatInterval) : 0
        return formatNumber(interval, {
          style: 'unit',
          unitDisplay: 'long',
          unit: getReminderUnitsForFormatNumber(intervalTypeId),
        })
      }
      if (settings?.shouldSum) {
        const unitForDateTime = getReminderUnitsForDateTime(intervalTypeId) as string
        const interval = repeatInterval ? Number(repeatInterval) : 0
        const duration = Duration.fromObject({ [unitForDateTime]: interval })

        return reminderDate.plus(duration).toFormat('D')
      }
      return reminderDate.toFormat('D')
    })
    .with(REMINDER_NOT_CONFIGURED, () => REMINDER_NOT_CONFIGURED)
    .exhaustive()
}

export const getReminderCriteriaType = (
  criteriaId: AdminReminderTypes.CriteriaType['reminder_criteria_type_id'] | null,
) =>
  match(criteriaId)
    .with('1', () => 'DISTANCE' as const)
    .with('2', () => 'HOS' as const)
    .with('3', () => 'DATE' as const)
    .with('4', () => 'HOS' as const)
    .with(null, () => REMINDER_NOT_CONFIGURED)
    .exhaustive()

export const convertReminderDistanceToMeters = ({
  distance,
  shouldUseMiles,
}: {
  distance: number | null
  shouldUseMiles: boolean
}): number | null => {
  if (R.isNullish(distance)) {
    return null
  }
  const kilometers = shouldUseMiles ? milesToKilometers(distance) : Number(distance)

  return kilometers * 1000
}

export const convertReminderDistanceToKilometersOrMiles = <
  Meters extends number | null,
>({
  meters,
  shouldUseMiles,
}: {
  meters: Meters
  shouldUseMiles: boolean
}): Meters extends null ? number | null : number => {
  if (R.isNullish(meters)) {
    return null as any
  }
  const distance = shouldUseMiles ? kilometersToMiles(meters as number) : Number(meters)

  return distance / 1000
}

export const createOverviewStyleClasses = (theme: KarooUiInternalTheme) => ({
  '&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell': {
    py: 0,
  },
  '&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell': {
    py: 1,
  },
  '&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell': {
    py: 2,
  },

  // custom classes
  '& .reminder-edit-category-name': {
    padding: 0,
    '& .MuiDataGrid-columnHeaderTitleContainerContent': {
      width: '100%',
    },
  },
  '& .reminder-not-configured': {
    backgroundColor: opacify(-0.92, theme.palette.secondary.main),
  },
  '& .reminder-expired': {
    backgroundColor: opacify(-0.92, theme.palette.error.main),
  },
  '& .reminder-overdue': {
    backgroundColor: opacify(-0.92, theme.palette.warning.main),
  },
})

export function roundMetersToNearestKilometer<
  T extends `${number}` | number | string | null,
>(
  value: T,
  criteriaTypeId: AdminReminderTypes.CriteriaType['reminder_criteria_type_id'] | null,
) {
  return getReminderCriteriaType(criteriaTypeId) === 'DISTANCE' && value
    ? (round(Number(value as string), -3).toString() as T)
    : value
}

export function formatTranslationOrCustomStringMetaObject(
  categoryName: TranslationOrCustomStringMetaObject,
): string {
  return categoryName.type === 'translationId'
    ? ctIntl.formatMessage({ id: categoryName.translationId })
    : categoryName.customString
}

export function formatCategoryNameStringMetaObject(
  categoryName: ReminderCategoryTranslationOrCustomStringMetaObject,
): string {
  if (categoryName.type === 'pre-defined') {
    return categoryName.translationId
      ? ctIntl.formatMessage({
          id: categoryName.translationId,
          defaultMessage: categoryName.fallbackString,
        })
      : categoryName.fallbackString
  }
  return categoryName.customString
}
