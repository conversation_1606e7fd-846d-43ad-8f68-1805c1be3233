import { Box, styled } from '@karoo-ui/core'

type Props = { children: React.ReactNode; className?: string }

const BaseContentContainer = ({ children, className }: Props) => (
  <Box
    id="content-container"
    className={className}
  >
    {children}
  </Box>
)

// Useful to prevent the "double" scrollbar issue on certain pages
const ContentContainerNonScrollable = styled(BaseContentContainer)({
  position: 'relative',
  display: 'flex',
  flexGrow: 1,
  flexDirection: 'column',
  height: '100%',
  overflow: 'hidden',
})

export default ContentContainerNonScrollable

export const ContentContainerScrollable = styled(ContentContainerNonScrollable)({
  overflow: 'auto',
})
