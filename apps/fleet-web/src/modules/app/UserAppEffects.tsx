import { PureComponent, useEffect } from 'react'
import { isEmpty, kebabCase } from 'lodash'
import * as Sentry from '@sentry/browser'
import { connect } from 'react-redux'
import { useLocation, withRouter, type RouteComponentProps } from 'react-router'

import {
  getAlertCenterSetting,
  getLoginApiData,
  getSettings_UNSAFE,
  getUser,
} from 'duxs/user'
import { getNpsScorePopupEnable } from 'duxs/user-sensitive-selectors'
import { getHardwareTypes } from 'duxs/vehicle-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'

import { onMapPath } from 'cartrack-utils'
import FeedbackButton from './components/FeedbackButton'
import ChangePasswordReminderModal from './components/modals/change-password-reminder'
import MessageModal from './components/modals/message'
import Sidebar from './components/Sidebar'
import GlobalDrawers from './GlobalDrawers'
import GlobalModals from './GlobalModals'
import NewAlertsSnackbarWithNotification from './NewIncidentsAlert'
import SystemStateModal from './system-state-modal'
import { UserAppEffectsTestableModals } from './UserAppEffectsTestableModals'
import { UserInactivitySideEffects } from './UserInactivitySideEffects'
import { UserIndustrySelection } from './UserIndustrySelection'
import { UserNpsScoreSelection } from './UserNpsScoreSelection'

type Props = ReturnType<typeof mapStateToProps> & RouteComponentProps

class UserAppEffects extends PureComponent<Props> {
  componentDidUpdate(prevProps: Props) {
    const { location, history, user, hardwareTypes } = this.props

    if (prevProps.location !== location) {
      const root = document.getElementById('router-root')
      if (root) {
        window.scrollTo(0, 0)
        root.scrollTo(0, 0)
      }
    }

    if (
      user &&
      ((prevProps.user && prevProps.user.id !== user.id) ||
        isEmpty(prevProps.hardwareTypes)) &&
      hardwareTypes.length > 0 &&
      onMapPath(location.pathname)
    ) {
      // Should run once after login in and if there are hardware types
      const mapHardwareTypeSubNavs = hardwareTypes.map((item) => kebabCase(item))
      const isSubNavHardwareType = mapHardwareTypeSubNavs.includes(
        location.pathname.replace('/map/', ''),
      )
      if (!isSubNavHardwareType) {
        const defaultNewPath = `/map/${mapHardwareTypeSubNavs[0]}`
        history.push(defaultNewPath)
      }
    }
  }

  render() {
    return <UserAppEffectsFunctional />
  }
}

const mapStateToProps = (state: AppState) => ({
  user: getUser(state) ?? null,
  hardwareTypes: getHardwareTypes(state) ?? null,
})

export default withRouter(connect(mapStateToProps)(UserAppEffects))

const UserAppEffectsFunctional = () => {
  const location = useLocation()

  const { isAdmin, enableClientFeedback } = useTypedSelector(getSettings_UNSAFE)
  const user = useTypedSelector(getUser) ?? null
  const loginApiData = useTypedSelector(getLoginApiData)
  const alertCenterSetting = useTypedSelector(getAlertCenterSetting)
  const npsScorePopupEnable = useTypedSelector(getNpsScorePopupEnable)

  useEffect(() => {
    if (ENV.NODE_ENV === 'production' && user) {
      Sentry.getCurrentScope().setUser({
        username: user.username,
        id: user.id,
      })
      Sentry.getCurrentScope().setTag('company_name', user.companyName)
    }
  }, [user])

  return (
    <div className="App">
      {user && (
        <>
          <UserInactivitySideEffects />
          {loginApiData.status === 'SUCCEEDED' &&
            loginApiData.loginMethodType === 'credentials_login' &&
            isAdmin && <UserIndustrySelection />}
          {loginApiData.status === 'SUCCEEDED' &&
            loginApiData.loginMethodType === 'credentials_login' &&
            npsScorePopupEnable && <UserNpsScoreSelection />}
          {enableClientFeedback && <FeedbackButton />}
          {alertCenterSetting && <NewAlertsSnackbarWithNotification />}

          <Sidebar
            currentPath={location.pathname}
            user={user}
          />

          <ChangePasswordReminderModal />
          <MessageModal />
          <SystemStateModal />

          <GlobalModals />
          <GlobalDrawers />

          <UserAppEffectsTestableModals />
        </>
      )}
    </div>
  )
}
