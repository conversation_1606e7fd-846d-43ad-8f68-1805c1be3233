import { getReduxStore_DANGER } from 'src/ReduxStoreSyncProvider'

// We use getReduxStore_DANGER as a workaround. All this file is bad practice. Just trying to keep es modules working at this point while we don't remove this code
const getState = () => getReduxStore_DANGER().getState()

function getMessages() {
  const {
    locale: { messages },
  } = getState()
  return messages
}

export function formatSimpleMessage(message: string, defaultTxt = null) {
  const messages = getMessages()
  return messages[message] || defaultTxt || message
}
