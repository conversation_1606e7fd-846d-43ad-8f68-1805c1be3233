import { findIndex, has, head, isEmpty, isNil, isObject, last } from 'lodash'
import { flow, keyBy, map } from 'lodash/fp'
import moment from 'moment-timezone'
import * as R from 'remeda'
import { z } from 'zod'

import type { ClientUserId } from 'api/types'
import { mainStyleSettings } from 'duxs/client-settings-helper'
import type { Ct_refresh_token, MapApiProvider } from 'src/api/user/types'
import type { UserSchema } from 'src/modules/admin/user-settings/shared/schemas'
import type { LoginSchema } from 'src/modules/app/authentication/login'
import type { DropdownOption, FixMeAny, PromiseResolvedType } from 'src/types'
import type { NonEmptyArray } from 'src/types/utils'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'

import {
  generateImgUriFromBase64,
  isNilOrTrimmedEmptyString,
  isTrue,
  parseEnvironmentConfig,
  uuid,
} from 'cartrack-utils'
import apiCaller, { apiCallerNoX } from '../api-caller'
//import { whiteLabelStyleObjectForDebugging } from './__scripts__/white-label'

import {
  countryWebsiteSchema,
  ctLanguageIdSchema,
  emailSchema,
  type CartrackLocale,
  type Ct_fleet_contact_us,
  type GetPreLoginData,
  type Login,
  type MatchEmailOrSmsOtp,
  type ResetPasswordApi,
  type ResetPasswordType,
  type ResetSubuserPasswordApi,
  type SendOtpByEmailOrSms,
  type SignupType,
  type UpdateUserVehiclePositionSetting,
} from './types'
import {
  normalizeKarooUiThemeSetting,
  parseKarooUiThemeSetting,
  type StringifiedKarooUiCustomizableTheme,
} from './utils'

const tzNameMap = {
  'US/Alaska': 'America/Anchorage',
  'US/Pacific': 'America/Los_Angeles',
  'US/Central': 'America/Chicago',
  'US/Hawaii': 'Pacific/Honolulu',
  'US/Eastern': 'America/New_York',
  'US/Mountain': 'America/Boise',
}

const datetimeFormats = [
  'ISO [Y-m-d H:i:sO]',
  'US [m/d/Y H:i:sO T]',
  'EU and others [d-m-Y H:i:sO]',
]

export const apiCoachingDashboardFilterTypeSchema = z.enum([
  'vehicle_group_init',
  'department_init',
  'sub_department_init',
  'drivers_init',
  'event_type_init',
])

export declare namespace CoachingDashboardFilterType {
  type ApiInput = {
    id: z.infer<typeof apiCoachingDashboardFilterTypeSchema>
    default: string
  }
  type ParsedEvent = {
    id: 'vehicleGroup' | 'department' | 'section' | 'driver' | 'eventType'
    value: string
  }
}

export function parseUserSettings(settings: Record<string, FixMeAny>) {
  const userImage = generateImgUriFromBase64(settings.logo_image_base64) || undefined

  const isUserSubUser = settings.client_user_id !== ''

  const mifleetAdditionalSettings: {
    cell_number: FixMeAny
    mifleetName?: FixMeAny
    mifleetLanguageId?: FixMeAny
    mifleetLanguagesOptions?: Array<{ value: FixMeAny; name: FixMeAny }>
  } = {
    cell_number: settings.cell_number,
  }
  // These settings are not always present so this is to avoid them from overriding existing settings.
  if (has(settings, 'mifleet_name')) {
    mifleetAdditionalSettings.mifleetName = settings.mifleet_name
  }
  if (has(settings, 'mifleet_language_id')) {
    mifleetAdditionalSettings.mifleetLanguageId = settings.mifleet_language_id
  }
  if (has(settings, 'mifleet_languages')) {
    mifleetAdditionalSettings.mifleetLanguagesOptions = settings.mifleet_languages
      ? settings.mifleet_languages.map((lan: FixMeAny) => ({
          value: lan.language_id,
          name: lan.tag,
        }))
      : []
  }

  return {
    address: settings.address || '--',
    city: settings.city || '--',
    state: settings.state || '--',
    zipCode: settings.zip_code || '--',
    /**
     *  @deprecated - use primaryEmail instead that has a proper redux selector
     */
    email: settings.primary_email || '--',
    primaryEmail: settings.primary_email,
    phone: settings.phone,
    companyName: settings.customer_name,
    datetimeFormat: datetimeFormats[settings.datetime_format - 1],
    DOTNumber: settings.carriers_dot_number || '--',
    GPSFormat: settings.gps_format,
    userImage,
    timeZone: settings.timezone_of_user,
    useLocalTime: isTrue(settings.timezone_from_pc),
    isAdmin: !isUserSubUser,
    isSubUser: isUserSubUser,
    vehiclePopoverList: settings.popover_settings,
    dashboardIndustry: settings.dashboard_industry,
    dashboardTopics: settings.dashboard_topic,
    dashboardVehicleDisplayName: settings.dashboard_vehicle_field,
    ssoHash: settings.sso_hash,
    apiKey: settings.api_key,
    roadDescriptionType: settings.road_description_type,
    mifleetImportDataV2: settings.mifleetImportDataV2,

    ...mifleetAdditionalSettings,
    ...(isUserSubUser
      ? {
          clientUserId: settings.client_user_id,
          subUserCanEditMainUsersGeofences: isTrue(
            settings.allow_subuser_toedit_mainuser_geofences,
          ),
          subUserGeofencesCanBeEditedByMainUsers: isTrue(
            settings.allow_mainuser_toedit_subuser_geofences,
          ),
          subUserCanSeeMainUsersGeofences: isTrue(
            settings.allow_subuser_tosee_mainuser_geofences,
          ),
          subUserGeofencesCanBeSeenByMainUsers: isTrue(
            settings.allow_mainuser_tosee_subuser_geofences,
          ),
          subUserCanEditMainUsersPOI: isTrue(
            settings.allow_subuser_toedit_mainuser_poi,
          ),
          subUserPOICanBeEditedByMainUsers: isTrue(
            settings.allow_mainuser_toedit_subuser_poi,
          ),
          subUserCanSeeMainUsersPOI: isTrue(settings.allow_subuser_tosee_mainuser_poi),
          subUserPOICanBeSeenByMainUsers: isTrue(
            settings.allow_mainuser_tosee_subuser_poi,
          ),
        }
      : {}),
  }
}

function parseCustomDataEntity(customDataEntity: string | undefined) {
  const customFieldsSettings: Record<string, boolean> = {}

  if (!isNil(customDataEntity)) {
    customFieldsSettings.vehicleCustomDataEntity = isTrue(
      JSON.parse(customDataEntity).vehicle,
    )
    customFieldsSettings.driverCustomDataEntity = isTrue(
      JSON.parse(customDataEntity).driver,
    )
  }
  return customFieldsSettings
}

function parseSettings(
  settings: Record<string, FixMeAny>,
  appSettings: Record<string, FixMeAny> & {
    karooUiTheme?: StringifiedKarooUiCustomizableTheme | null
  } = {},
) {
  const {
    styleCustomerMainLogo,
    styleCustomerMenuLogo,

    userGuideFilename = 'FleetManual.pdf',
    styleAppName,
    styleMainLogoWidthPx,
    styleFavicon,

    // Sidebar colors
    styleNavbarColour,
    styleSubnavbarColour,
    styleSidebarMenuHeaderIconPath,
    styleSidebarMenuFooterLogoPath,
    styleSidebarMenuFooterIconPath,
    styleSidebarMenuActiveFontColour,
    styleSidebarMenuInactiveFontColour,
    styleSidebarMenuHoverFontColour,
    styleSidebarMenuHoverColour,
    styleSidebarMenuActiveColour,
    styleSidebarSubMenuInactiveFontColour,

    styleMenuActiveFontColour,
    styleMenuInactiveFontColour,

    styleActiveButtonsColour,
    styleMenuActiveIconColour,
    styleMenuInactiveIconColour,
    styleSubmenuActiveColour,
    styleTableColumnFontColourActive,
    styleInputfieldColourActive,
    styleInputfieldColourSelection,
    styleInputfieldColourHover,
    styleInputfieldIconColourStandard,
    styleInputfieldIconColourActive,
    styleLoadingBarColour,
    styleLoadingSpinnerColour,
    styleIconColour,
    styleAlternateTextColour,
    styleSuperscriptCounterColour,
    styleButtonDefaultColourActive,
    styleButtonDefaultTextColourActive,
    styleButtonDefaultColourStandard,
    styleButtonDefaultTextColourStandard,
    styleButtonDefaultColourHover,
    styleButtonDefaultTextColourHover,
    styleButtonDefaultColourDisabled,
    styleButtonDefaultTextColourDisabled,
    styleButtonActionColourActive,
    styleButtonActionTextColourActive,
    styleButtonActionColourStandard,
    styleButtonActionTextColourStandard,
    styleButtonActionColourHover,
    styleButtonActionTextColourHover,
    styleButtonActionColourDisabled,
    styleButtonActionTextColourDisabled,
    styleButtonLinkColourActive,
    styleButtonLinkTextColourActive,
    styleButtonLinkColourStandard,
    styleButtonLinkTextColourStandard,
    styleButtonLinkColourHover,
    styleButtonLinkTextColourHover,
    styleButtonLinkColourDisabled,
    styleButtonLinkTextColourDisabled,
    styleButtonRedColourActive,
    styleButtonRedTextColourActive,
    styleButtonRedColourStandard,
    styleButtonRedTextColourStandard,
    styleButtonRedColourHover,
    styleButtonRedTextColourHover,
    styleButtonRedColourDisabled,
    styleButtonRedTextColourDisabled,
    styleButtonGreenColourActive,
    styleButtonGreenTextColourActive,
    styleButtonGreenColourStandard,
    styleButtonGreenTextColourStandard,
    styleButtonGreenColourHover,
    styleButtonGreenTextColourHover,
    styleButtonGreenColourDisabled,
    styleButtonGreenTextColourDisabled,
    styleButtonRaisedColourActive,
    styleButtonRaisedTextColourActive,
    styleButtonRaisedColourStandard,
    styleButtonRaisedTextColourStandard,
    styleButtonRaisedColourHover,
    styleButtonRaisedTextColourHover,
    styleButtonRaisedColourDisabled,
    styleButtonRaisedTextColourDisabled,
    styleButtonWhiteColourActive,
    styleButtonWhiteTextColourActive,
    styleButtonWhiteColourStandard,
    styleButtonWhiteTextColourStandard,
    styleButtonWhiteColourHover,
    styleButtonWhiteTextColourHover,
    styleButtonWhiteColourDisabled,
    styleButtonWhiteTextColourDisabled,
    styleMainLogoPoweredBy,
    styleMenuLogoPoweredBy,
    styleLogoPoweredByType,
    styleLoginBackgroundColour,
    styleLoginTextColour,
    styleLoginFooterLogo,
    styleLoginFooterBackgroundColor,
    styleLoginFooterHeight,
    styleLoginForgotUsername,
    styleLoginLanguage,
    styleLoginModalFooter,
    styleLoginSignUp,
    styleLoginStayLoggedIn,
    styleLoginLogoInsideBox,
    styleLogoPoweredByType3Label,
    vehicleEnableMaxSpeed = false,
    distanceInMiles,
    passwordMaxAge = 120, // Days
    roadSpeed = true,
    karooUiTheme,
    customDataEntity,
    overspeedThreshold,

    ...remainingAppSettings
  } = appSettings

  const customFieldsSettings: Record<string, boolean> =
    parseCustomDataEntity(customDataEntity)

  const parsedSettings = {
    ...parseUserSettings(settings),
    userGuideFilename,
    ...customFieldsSettings,
    ...remainingAppSettings,
    styleProperties: {
      styleCustomerMainLogo,
      styleCustomerMenuLogo,

      styleAppName,
      styleFavicon,

      // Sidebar colors
      styleNavbarColour,
      styleSubnavbarColour,
      styleSidebarMenuHeaderIconPath,
      styleSidebarMenuFooterLogoPath,
      styleSidebarMenuFooterIconPath,
      styleSidebarMenuActiveFontColour,
      styleSidebarMenuInactiveFontColour,
      styleSidebarMenuHoverFontColour,
      styleSidebarMenuHoverColour,
      styleSidebarMenuActiveColour,
      styleSidebarSubMenuInactiveFontColour,

      styleMenuActiveFontColour,
      styleMenuInactiveFontColour,

      styleActiveButtonsColour,
      styleMenuActiveIconColour,
      styleMenuInactiveIconColour,
      styleSubmenuActiveColour,
      styleTableColumnFontColourActive,
      styleInputfieldColourActive,
      styleInputfieldColourSelection,
      styleInputfieldColourHover,
      styleInputfieldIconColourStandard,
      styleInputfieldIconColourActive,
      styleLoadingBarColour,
      styleLoadingSpinnerColour,
      styleIconColour,
      styleAlternateTextColour,
      styleSuperscriptCounterColour,
      styleButtonDefaultColourActive,
      styleButtonDefaultTextColourActive,
      styleButtonDefaultColourStandard,
      styleButtonDefaultTextColourStandard,
      styleButtonDefaultColourHover,
      styleButtonDefaultTextColourHover,
      styleButtonDefaultColourDisabled,
      styleButtonDefaultTextColourDisabled,
      styleButtonActionColourActive,
      styleButtonActionTextColourActive,
      styleButtonActionColourStandard,
      styleButtonActionTextColourStandard,
      styleButtonActionColourHover,
      styleButtonActionTextColourHover,
      styleButtonActionColourDisabled,
      styleButtonActionTextColourDisabled,
      styleButtonLinkColourActive,
      styleButtonLinkTextColourActive,
      styleButtonLinkColourStandard,
      styleButtonLinkTextColourStandard,
      styleButtonLinkColourHover,
      styleButtonLinkTextColourHover,
      styleButtonLinkColourDisabled,
      styleButtonLinkTextColourDisabled,
      styleButtonRedColourActive,
      styleButtonRedTextColourActive,
      styleButtonRedColourStandard,
      styleButtonRedTextColourStandard,
      styleButtonRedColourHover,
      styleButtonRedTextColourHover,
      styleButtonRedColourDisabled,
      styleButtonRedTextColourDisabled,
      styleButtonGreenColourActive,
      styleButtonGreenTextColourActive,
      styleButtonGreenColourStandard,
      styleButtonGreenTextColourStandard,
      styleButtonGreenColourHover,
      styleButtonGreenTextColourHover,
      styleButtonGreenColourDisabled,
      styleButtonGreenTextColourDisabled,
      styleButtonRaisedColourActive,
      styleButtonRaisedTextColourActive,
      styleButtonRaisedColourStandard,
      styleButtonRaisedTextColourStandard,
      styleButtonRaisedColourHover,
      styleButtonRaisedTextColourHover,
      styleButtonRaisedColourDisabled,
      styleButtonRaisedTextColourDisabled,
      styleButtonWhiteColourActive,
      styleButtonWhiteTextColourActive,
      styleButtonWhiteColourStandard,
      styleButtonWhiteTextColourStandard,
      styleButtonWhiteColourHover,
      styleButtonWhiteTextColourHover,
      styleButtonWhiteColourDisabled,
      styleButtonWhiteTextColourDisabled,
      styleMainLogoPoweredBy,
      styleMenuLogoPoweredBy,
      styleMainLogoWidthPx,
      styleLogoPoweredByType,
      styleLoginBackgroundColour,
      styleLoginTextColour,
      styleLoginFooterLogo,
      styleLoginFooterBackgroundColor,
      styleLoginFooterHeight,
      styleLoginForgotUsername,
      styleLoginLanguage,
      styleLoginModalFooter,
      styleLoginSignUp,
      styleLoginStayLoggedIn,
      styleLoginLogoInsideBox,
      styleLogoPoweredByType3Label,
      karooUiTheme: parseKarooUiThemeSetting(karooUiTheme),
      // ...whiteLabelStyleObjectForDebugging,
    },
    vehicle_enable_max_speed: isTrue(vehicleEnableMaxSpeed),
    showingOverspeedThreshold: isTrue(overspeedThreshold),
    distanceInMiles: Boolean(distanceInMiles),
    miles_unit: isTrue(distanceInMiles) ? 'Feet' : 'Meters',
    passwordMaxAge,
    roadSpeed: isTrue(roadSpeed),
  }

  return parsedSettings as typeof parsedSettings & Record<string, any>
}

export function normalizeUserProfileSettings(
  { costs, isSubUser }: { costs: boolean; isSubUser: boolean },
  formData: UserSchema['profile'] &
    UserSchema['settings'] & {
      imgSrc: string | null
      datetimeFormat: (typeof datetimeFormats)[number]
    },
) {
  const {
    GPSFormat,
    roadDescriptionType,
    imgSrc,
    email,
    cellPhone,
    timeZone,
    useLocalTime,
  } = formData
  let updates: {
    gps_format: unknown
    road_description_type: unknown
    logo: string | null
    timezone_of_user: string | null
    timezone_from_pc: boolean
    email?: unknown
    mobile_number?: unknown
    mifleet?: {
      user_name: string
      language_id: string
    }
  } = {
    gps_format: GPSFormat,
    road_description_type: roadDescriptionType,
    logo: imgSrc,
    timezone_of_user: timeZone,
    timezone_from_pc: useLocalTime,
  }

  if (isSubUser) {
    updates = { ...updates, email: email, mobile_number: cellPhone }
  }

  if (costs && 'languageId' in formData) {
    updates = {
      ...updates,
      mifleet: {
        user_name: formData.name,
        language_id: formData.languageId,
      },
    }
  }

  return updates
}

function parsePreLoginData(res: GetPreLoginData.ApiOutput) {
  const languages: {
    defaultSelectedIndex: number
    options: Array<{
      name: string
      value: CartrackLocale
      default: boolean
    }>
  } = {
    defaultSelectedIndex: -1,
    options: [],
  }

  const defLang: Array<GetPreLoginData.CtLanguage> = [
    {
      id: 'en-ZA',
      description: 'English',
      default: 'true',
    },
  ]

  // eslint-disable-next-line no-nested-ternary
  const langArray = isNil(res.languages)
    ? defLang
    : isObject(res.languages)
      ? res.languages
      : (JSON.parse(res.languages) as Array<GetPreLoginData.CtLanguage>)

  languages.options = langArray.map((lang) => ({
    name: lang.description,
    value: safeParseFromZodSchema(ctLanguageIdSchema, lang.id, {
      defaultValue: () => 'en',
    }),
    default: isTrue(lang.default),
  }))

  languages.defaultSelectedIndex = findIndex(languages.options, (o) =>
    isTrue(o.default),
  )

  const {
    basicLogin,
    googlePlayLink = 'https://play.google.com/store/apps/details?id=com.cartrack.fleet&hl=en_SG', // Default to SG
    appStoreLink = 'https://itunes.apple.com/sg/app/cartrack-fleet/id498444696?mt=8', // Default to SG
    eld,
    contactSupportEmail,
    defaultCountry,
    defaultissuingcountry,
    mobileStoreLinks,
    karooUiTheme,
    disableThirdPartyLogging,
    useFederationLoginOnly,
    enableWelcomePage,
    contactEmailEnabled,
    contactSmsEnabled,
    ...styleProperties
  } = res.appSettings

  const preLoginSettings = {
    styleProperties: {
      ...styleProperties,
      karooUiTheme: parseKarooUiThemeSetting(karooUiTheme),
      //...whiteLabelStyleObjectForDebugging,
    },
    googlePlayLink,
    appStoreLink,
    basicLogin,
    eld,
    defaultCountry,
    defaultissuingcountry,
    mobileStoreLinks,
    disableThirdPartyLogging: disableThirdPartyLogging === true,
    useFederationLoginOnly,
    enableWelcomePage,
    contactEmailEnabled,
    contactSmsEnabled,
    contactSupportEmail,
  }

  const { ctCountries, federatedLogins, countriesWebsites: rawCountriesWebsites } = res

  const countriesWebsites = (():
    | NonEmptyArray<{
        countryName: string
        websiteLink: string
        countryFlagUrl: string | null
      }>
    | false => {
    if (!R.isArray(rawCountriesWebsites)) {
      return false
    }
    const returnValue = Array_filterMap(
      rawCountriesWebsites,
      (website, { RemoveSymbol }) => {
        const parseResult = countryWebsiteSchema.safeParse(website)
        if (!parseResult.success) {
          return RemoveSymbol
        }
        const parsedWebsite = parseResult.data
        return {
          countryName: parsedWebsite.countryName,
          websiteLink: parsedWebsite.countryWebsite,
          countryFlagUrl: parsedWebsite.countryFlagUrl ?? null,
        }
      },
    ).sort((a, b) => a.countryName.localeCompare(b.countryName))

    if (returnValue.length === 0) {
      return false
    }
    return returnValue as unknown as NonEmptyArray<(typeof returnValue)[number]>
  })()

  return {
    ctCountries,
    languages,
    federatedLogins,
    settings: preLoginSettings,
    countriesWebsites,
  }
}

function parseTimeZones(
  timeZones: Login.SuccessfulLogin['ct_fleet_get_timezones_list'],
) {
  return timeZones.map((timeZone) => {
    const tzName = (tzNameMap as FixMeAny)[timeZone.boost_timezone_name]

    const tzOffset = tzName ? moment().hours(12).tz(tzName).utcOffset() / 60 : 0

    return {
      description: timeZone.timezone_description,
      id: timeZone.timezone_id,
      ianaName: (tzNameMap as FixMeAny)[timeZone.boost_timezone_name],
      tzOffset,
    }
  })
}

function normalizeHelpRequest(
  request: Ct_fleet_contact_us.ApiFnInput,
): Ct_fleet_contact_us.ApiInput {
  return {
    contact_person: request.name,
    contact_email: request.email,
    contact_number: request.phoneNumber,
    query_type: `FleetWeb${request.type ? ` - ${request.type}` : ''}`,
    vehicle: '',
    query: request.message,
    files: request.uploadedFiles.map((file) => {
      const fileMimeType = last(head(file.data.split(';base64'))?.split('data:'))
      const base64 = last(file.data.split('base64,'))
      return {
        file_name: file.name,
        file_mime_type: fileMimeType,
        base64_data: base64,
      }
    }),
  }
}

function parseHardwareTypeList(rawRes: FixMeAny) {
  const data = flow(
    map((item: FixMeAny) => {
      const object = { hardwareTypeListID: item.hardware_type_list_id }
      const subData = flow(
        map((subItem: FixMeAny) => {
          const splitItem = subItem.split(';')
          return {
            hardwareTypeID: Number(splitItem[0]),
            isBasic: isTrue(splitItem[1]),
          }
        }),
        keyBy('hardwareTypeID'),
      )(item.hardware_type_id)

      return { ...object, hardwareTypeData: subData }
    }),
    keyBy('hardwareTypeListID'),
  )(rawRes)

  const typedData = data as FixMeAny

  return typedData as Record<
    number,
    {
      hardwareTypeListID: string
      hardwareTypeData: Record<number, { hardwareTypeID: number; isBasic: boolean }>
    }
  >
}

/**
 * Format vehicle list to parse into select library
 * TODO: Remove this parser and migrate conversion to backend after development-refactor branch has been merged in PHP
 * @param vehicleList
 */
function parseVehicleList(vehicleList: Array<DropdownOption>) {
  return vehicleList.map((vehicle) => {
    const parsedVehicle = {
      label: vehicle.name,
      ...vehicle,
    }

    return parsedVehicle
  })
}

function parseTwoFAMethods(res: Login.TwoFALogin) {
  // verify the email
  const parseEmailResult = emailSchema.nullable().safeParse(res.email)

  return {
    email: parseEmailResult.success ? parseEmailResult.data : '',
    phone: res.phone,
  }
}

function parseSuccessfulLogin(res: Login.SuccessfulLogin, authToken: string) {
  // NOTE: get the access token for tfms service
  const accessToken =
    res.ct_fleet_tfms_services_api && 'value' in res.ct_fleet_tfms_services_api
      ? res.ct_fleet_tfms_services_api?.value?.accessToken
      : null

  return {
    status: res.status,
    accessToken,
    user: {
      id: res.user_id,
      account: res.account,
      username: res.username,
      companyName: res.ct_fleet_get_user_settings.customer_name,
      primaryEmail: res.primaryEmail,
      cuid: isNilOrTrimmedEmptyString(res.client_user_id)
        ? null
        : (res.client_user_id as ClientUserId),
      authToken,
    },
    timeZones: parseTimeZones(res.ct_fleet_get_timezones_list),
    settings: parseSettings(
      {
        ...res.ct_fleet_get_user_settings,

        client_user_id: res.client_user_id,
      },
      {
        ...res.ct_fleet_get_app_settings,
        mapApiProviderAuth: res.mapApiProviderAuth,
      },
    ) as ReturnType<typeof parseSettings> & {
      mapApiProvider: MapApiProvider
    },
    diagnosticStatus: res.ct_diagnostic_get_status
      ? res.ct_diagnostic_get_status[0]
      : {},
    diagnostic: res.ct_diagnostic_get_vehicles || {},
    disabled: res.disabled,
    disable_reason: res.disable_reason,
    passwordAge: res.ct_fleet_get_latest_password_change
      ? res.ct_fleet_get_latest_password_change[0].password_changed_ts
      : null,
    hardwareTypeList: parseHardwareTypeList(res.ct_fleet_get_hardware_type_list),
    sisense: {
      jwt:
        res.sisense_auth && res.sisense_auth.token ? res.sisense_auth.token : undefined,
    },
    serverId: res.server_id,
    ...(res.status_2fa ? { status2FA: res.status_2fa } : {}),
  } as const
}

function parseFailedLogin(res: Login.FailedLogin) {
  type Data =
    | {
        error: 'DISABLED_ACCOUNT'
        message: string | null
        reasonType: Login.FailedLogin_DisabledAccount['reason_type']
      }
    | {
        error: 'WRONG_CREDENTIALS'
        attemptsRemaining: number
      }
    | {
        error: 'TEMP_LOGIN_LOCK'
        minutesToUnlock: number
      }
    | {
        error:
          | 'INSECURE_LOGIN'
          | 'NO_ACTIVE_CONTRACTS'
          | 'LOCKED_ACCOUNT'
          | 'MAX_ATTEMPTS_EXCEEDED'
          | 'LOGIN_IP_LOCKED'
          | 'MOBILE_CONTRACT_ONLY'
      }

  const data = ((): Data => {
    switch (res.status) {
      case 'DISABLED_ACCOUNT': {
        return {
          error: res.status,
          reasonType: res.reason_type,
          // status_message is an empty string when there is none. To make this absence explicit on our side, we set message to null.
          message: isEmpty(res.status_message) ? null : res.status_message,
        }
      }
      case 'WRONG_CREDENTIALS': {
        return {
          error: res.status,
          attemptsRemaining: res.attempts_remaining,
        }
      }
      case 'TEMP_LOGIN_LOCK': {
        return {
          error: res.status,
          minutesToUnlock: Number(res.minutes_to_unlock),
        }
      }
      default: {
        return { error: res.status }
      }
    }
  })()

  return { status: 'FAILED', ...data } as const
}

const userApi = {
  fetchSystemStateMessage() {
    return apiCaller('ct_fleet_fetch_system_state', {}, {}).then((res: FixMeAny) => ({
      systemStateMessage: res.ct_fleet_fetch_system_state,
    }))
  },
  toggleSSOHash(data: FixMeAny) {
    return apiCaller('ct_fleet_enable_api_key', data).then(
      (res: FixMeAny) => res.sso_fetch_api_token,
    )
  },
  login({
    username,
    subUserUsername,
    password = '',
    locale,
    otp = '',
    vehicle = false,
    ssoToken,
  }: {
    username: string
    subUserUsername: string
    password: string | undefined
    locale: FixMeAny
    otp: string | undefined
    vehicle: false | string | undefined
    ssoToken?: FixMeAny
  }) {
    const authToken = uuid()
    let sso_token_object = {}
    if (ssoToken && ssoToken.length > 0) {
      sso_token_object = {
        session_only: false,
        login_source_id: 60,
        sso_token: ssoToken,
        account: username,
      }
    }

    return apiCaller('ct_login', {
      account: username,
      username: subUserUsername,
      password,
      locale,
      otp,
      browserName: '',
      version: ENV.APP_VERSION,
      environment: parseEnvironmentConfig(),
      thirdParty: vehicle,
      ...sso_token_object,
    }).then((res: Login.ApiOutput_CTLogin) => {
      if (res.status === 'SUCCEEDED') {
        return parseSuccessfulLogin(res, authToken)
      } else if (res.status === 'THIRD_PARTY_USER') {
        return {
          status: res.status,
          vehicleList: parseVehicleList(res.vehicle_list),
        } as const
      } else if (res.status === 'TWO_FACTOR_AUTHENTICATION') {
        return {
          status: res.status,
          loginData: {
            type: 'credentials_login',
            username,
            password,
            subUsername: subUserUsername,
            adminLogin: subUserUsername ? 'subuser' : 'admin',
            stayLoggedIn: false, // FIXME: seems like we do not use this value
          } satisfies LoginSchema & { type: 'credentials_login' },
          ...parseTwoFAMethods(res),
        } as const
      } else {
        return parseFailedLogin(res)
      }
    })
  },

  federatedLogin(federatedResponse: string) {
    const authToken = uuid()

    return apiCallerNoX<Login.ApiOutput_CTCartrackLogin>('ct_cartrack_login', {
      data: { federatedResponse },
    }).then((res) => {
      if (res.status === 'SUCCEEDED') {
        return parseSuccessfulLogin(res, authToken)
      } else if (res.status === 'THIRD_PARTY_USER') {
        return {
          status: res.status,
          vehicleList: parseVehicleList(res.vehicle_list),
        } as const
      } else if (res.status === 'TWO_FACTOR_AUTHENTICATION') {
        return {
          status: 'FEDERATED_TWO_FACTOR_AUTHENTICATION',
          loginData: {
            federatedResponse,
          },
          ...parseTwoFAMethods(res),
        } as const
      } else {
        return parseFailedLogin(res)
      }
    })
  },

  subUserLogin(subUserId: FixMeAny) {
    return apiCallerNoX('ct_relogin_as_subuser', { subUserId }).then(
      (res: FixMeAny) => res,
    )
  },

  tokenLogin({
    token,
    userId,
    vehicleId,
    clientId,
  }: {
    vehicleId: string
    userId: string
    token: string
    clientId?: string
  }) {
    const authToken = uuid()
    return apiCaller('ct_token_login', {
      userId,
      client_user_id: isNil(clientId) ? 0 : clientId,
      token,
      vehicleId,
    }).then((res: FixMeAny) => {
      if (res?.status === 'WRONG_CREDENTIALS') {
        throw new Error(res.status)
      }
      return {
        user: {
          id: res.user_id,
          username: res.username,
          companyName: res.ct_fleet_get_user_settings.customer_name,
          cuid: isNilOrTrimmedEmptyString(res.client_user_id)
            ? null
            : (res.client_user_id as ClientUserId),
          isTokenAccount: true as const,
          authToken,
        },
        /* OFF FOR NOW
         timeZones: parseTimeZones(res.ct_fleet_get_timezones_list)
        */
        settings: parseSettings(
          res.ct_fleet_get_user_settings,
          res.ct_fleet_get_app_settings,
        ),
      }
    })
  },

  sendOtpByEmailOrSms({ param, countryCode, userId }: SendOtpByEmailOrSms.Input) {
    return apiCaller(
      'ct_otp_by_email_or_sms',
      {
        param,
        country_code: countryCode,
        user_id: userId,
      },
      { noX: true },
    ).then((res: SendOtpByEmailOrSms.Output) => res)
  },

  matchEmailOrSmsOtp({
    methodValue,
    code,
    countryCode,
    userId,
  }: MatchEmailOrSmsOtp.Input) {
    return apiCaller(
      'ct_match_email_or_sms_otp',
      {
        email: methodValue,
        otp: code,
        user_id: userId,
        country_code: countryCode,
      },
      { noX: true },
    ).then((res: MatchEmailOrSmsOtp.Output) => res)
  },

  // update password in settings
  setUserPassword(password: string, currentPassword: string) {
    return apiCaller('ct_set_user_password', {
      password,
      currentPassword,
    })
  },

  // set new user password or reset on forgot password
  resetMainUserPassword({ securityCheck, password }: ResetPasswordType) {
    return apiCaller(
      'ct_change_password_procedure',
      {
        securityCheck: securityCheck,
        password,
      } satisfies ResetPasswordApi.ApiInput,
      { noX: true },
    ).then((res: ResetPasswordApi.ApiOutput) => res)
  },
  // set/update password for subUser
  resetSubUserPassword(resettingId: string, newPassword: string) {
    return apiCaller('ct_fleet_user_set_password', {
      security_check: resettingId,
      new_pwd: newPassword,
    }).then((res: ResetSubuserPasswordApi.ApiOutput) => res)
  },

  register({
    DOTNumber,
    companyName,
    firstName,
    lastName,
    phoneNumber,
    email,
    username,
    password,
  }: Record<string, any>) {
    return apiCaller('ct_create_register_client', {
      input_data: {
        dot_number: DOTNumber,
        company_name: companyName,
        first_name: firstName,
        last_name: lastName,
        mobile_number: phoneNumber,
        email_address: email,
        user_name: username,
        password,
      },
    }).then((res: FixMeAny) => res.create_register_client[0].out_message)
  },

  signup({
    type,
    name,
    companyName,
    email,
    phoneCountryCode,
    countryCode,
    phone,
    ownCar,
    numberVehicles,
    source,
  }: SignupType) {
    return apiCaller(
      'ct_signup',
      {
        data: {
          type,
          name,
          company_name: companyName,
          email,
          country_code: countryCode,
          phone_country_code: phoneCountryCode,
          phone_number: phone,
          own_a_car: ownCar,
          number_of_vehicles: numberVehicles,
          hear_about: source,
        },
      },
      { noX: true },
    ).then((res: FixMeAny) => res)
  },

  updateUserProfileSettings(
    meta: { costs: boolean; isSubUser: boolean },
    formData: Parameters<typeof normalizeUserProfileSettings>[1],
  ) {
    return apiCaller(
      'ct_fleet_update_user_profile_settings',
      normalizeUserProfileSettings(meta, formData),
    ).then((res: FixMeAny) => res)
  },

  updateProfileImage(base64Image: FixMeAny) {
    return apiCaller('ct_fleet_update_user_settings_seticon', { base64Image })
  },

  updateVehicleLivePosition({
    showLivePosition,
  }: UpdateUserVehiclePositionSetting.ApiInput) {
    return apiCallerNoX<UpdateUserVehiclePositionSetting.ApiOutput>(
      'ct_fleet_update_show_vehicle_live_position',
      {
        showLivePosition,
      },
    ).then((res) => ({
      newPrivacyHideLocationsFromDay: Number(
        res.ct_fleet_update_show_vehicle_live_position,
      ),
    }))
  },

  submitHelpRequest(request: Parameters<typeof normalizeHelpRequest>[0]) {
    return apiCaller('ct_fleet_contact_us', {
      request: normalizeHelpRequest(request),
    })
  },

  fetchPreLoginData() {
    return apiCaller('ct_fleet_get_prelogin_data', {}).then(
      (res: GetPreLoginData.ApiOutput) => parsePreLoginData(res),
    )
  },

  fetchUserSettings() {
    return apiCaller('ct_fleet_get_user_settings').then((res: FixMeAny) => {
      const { karooUiTheme, ...normalizedStyleSettings } = mainStyleSettings()

      return parseSettings(res.ct_fleet_get_user_settings, {
        ...normalizedStyleSettings,
        karooUiTheme:
          karooUiTheme == null ? null : normalizeKarooUiThemeSetting(karooUiTheme),
      })
    })
  },

  backendLogout() {
    return apiCaller('ct_logout')
  },

  // NOTE: here we use the tfms specific api to fetch the new access token
  async refreshJwtToken() {
    const result = await apiCallerNoX<Ct_refresh_token.ApiOutput>('ct_refresh_token')
    return { accessToken: result.value.accessToken }
  },
}

export default userApi

export type FailedLoginResponse = ReturnType<typeof parseFailedLogin>
export type SuccessfulLoginResponse = ReturnType<typeof parseSuccessfulLogin>
export type SuccessfulPreLoginResponse = PromiseResolvedType<
  typeof userApi.fetchPreLoginData
>

export type ParsedSettings = ReturnType<typeof parseSettings>
