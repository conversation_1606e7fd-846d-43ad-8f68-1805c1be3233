/* eslint-disable no-nested-ternary */
import { PureComponent } from 'react'
import { isNil, isPlainObject } from 'lodash'
import Downshift from 'downshift'
import {
  any,
  arrayOf,
  bool,
  element,
  func,
  number,
  oneOfType,
  shape,
  string,
} from 'prop-types'
import { connect } from 'react-redux'

import Icon from 'src/components/Icon'
import { GA4 } from 'src/shared/google-analytics4'

import { firstDefined, formatSimpleMessage, searchMatchSorter } from 'cartrack-utils'
import dropDownSort from '../util-functions/dropdown-sort-util'
import Checkbox from './checkbox'
import { ctIntl } from './ctIntl'
import TextInput from './text-input'

// Object purposely defined outside of class to avoid re-creating it in unpredictable ways (which can cause "Maximum update depth exceeded" type of errors ).
const defaultSelected = {
  name: null,
  description: null,
  description1: null,
  description2: null,
  value: null,
}

class InputDropdown extends PureComponent {
  constructor(props) {
    super(props)

    const defaultSelectedItem =
      props.defaultSelectedIndex === null
        ? undefined
        : props.options[this.props.defaultSelectedIndex]

    this.state = {
      options: this.resolveOptions(props, ''),
      optionsSorted: false,
      optionSelected: null,
      selectedItem: this.resolveSelected(props.activeOption),
      defaultSelectedItem,
      inputValue: '',
    }

    if (props.multiSelect && !props.activeOption)
      console.error(
        '[Cartrack] - Please provide a default activeOption object when using multiSelect',
      )
  }

  componentDidMount() {
    if (this.props.changeOnMount) {
      const option = firstDefined(
        this.props.activeOption || undefined, // Avoids default prop of null if not provided
        this.props.defaultSelectedItem,
        this.props.options[this.props.defaultSelectedIndex],
      )
      this.props.onChange((option && option.value) || option, this.props.id)
    }
  }

  componentDidUpdate(prevProps) {
    let newState
    if (this.props.options !== prevProps.options) {
      newState = {}
      newState.options = this.resolveOptions(this.props)
    }

    if (
      this.props.activeOption !== prevProps.activeOption ||
      (this.props.activeOption && !this.state.selectedItem) ||
      this.props.options !== prevProps.options
    ) {
      newState = newState || {}
      newState.selectedItem = this.resolveSelected(this.props.activeOption)
    }

    if (newState) this.setStateValue(newState)
  }

  setStateValue = (newState) => this.setState(newState)

  getRef = (el) => (this.el = el)

  handleDownshiftOnChange = (item, { highlightedIndex, setHighlightedIndex }) => {
    const { vehicleDisplayName, multiSelect, activeOption, onChange, id, returnItem } =
      this.props

    if (isNil(item)) {
      return
    }

    if (multiSelect) {
      const selected = { ...activeOption }
      if (item.value in selected) delete selected[item.value]
      else selected[item.value] = item
      onChange(selected)
      this.openMenu()
      setHighlightedIndex(highlightedIndex)
    } else if (returnItem) {
      onChange(item, id)
    } else {
      onChange(
        isNil(item.value) ? item[vehicleDisplayName] || item.name : item.value,
        id,
      )
    }

    if (this.el) this.el.blur()
    this.setState({
      forceAllOptions: false,
      inputValue: '',
    })
  }

  handleInputChange = (e) => {
    const value = e && e.currentTarget ? e.currentTarget.value : e
    if (this.props.onInputChange) {
      this.props.onInputChange(e)
    }

    const forceAllOptions = false

    this.setState({
      inputValue: value,
      options: this.resolveOptions(this.props, value, forceAllOptions),
      forceAllOptions,
    })

    if (this.props.clearable && !value) {
      this.props.onChange(null)
    }
  }

  clearInput = () => {
    this.handleInputChange('')
    if (this.el) this.el.focus()
  }

  // Event Handlers
  handleInputFocus = () => {
    if (!this.props.disableInput && this.el) {
      this.el.select()
    }

    const forceAllOptions = true

    this.setState({
      forceAllOptions,
      ...(this.props.resetOptionsOnFocus
        ? {
            options: this.resolveOptions(this.props, '', forceAllOptions),
            inputValue: '',
          }
        : {}),
    })
  }

  handleClick = () => {
    this.toggleMenu()
    const { analytics, onElementFocus, placeholder } = this.props
    if (analytics) {
      GA4.event({
        category: analytics.eventCategory,
        action: `${placeholder} Click`,
      })
    }
    if (onElementFocus) onElementFocus()
  }

  itemToString = (item) => {
    const { vehicleDisplayName, disableOptionIntl } = this.props

    if (disableOptionIntl) {
      return (item && item[vehicleDisplayName]) || item.name || ''
    }

    return formatSimpleMessage((item && item[vehicleDisplayName]) || item.name) || ''
  }

  itemsToString = (items) => {
    const keys = Object.keys(items)
    if (keys.length === 0) return undefined
    if (keys.length === 1) return this.itemToString(items[keys[0]])
    return keys.length + ' Selected'
  }

  resolveOptions(
    { options, disableInput, disableFilter },
    inputValue,
    forceAllOptions = true,
  ) {
    // Necessary to allow use within handleInputChange
    const filterStr = inputValue === undefined ? this.state.inputValue : inputValue

    if (
      disableFilter ||
      !filterStr ||
      disableInput ||
      (forceAllOptions && this.state.forceAllOptions)
    )
      return options

    // Fuzzy Search
    /* let filtered = options.filter((o, i, arr) => {
      // Allow custom transform to search single/compound values
      const item = itemSearchTransform ? itemSearchTransform(o) : o
      // Default to fuzzy string search
      return filterPredicate
        ? filterPredicate(item, filterStr, i, arr)
        : fuzzySearch(item, filterStr)
    }) */

    let filtered = searchMatchSorter(options, filterStr.trim())

    if (this.props.maxItems) filtered = filtered.slice(0, this.props.maxItems)
    return filtered
  }

  resolveSelected(item) {
    if (isPlainObject(item)) {
      return item
    }
    if (item === undefined) {
      return defaultSelected
    }
    return (
      this.props.options.find(
        (o) => item !== undefined && (o.value === item || o.name === item),
      ) || defaultSelected
    )
  }

  stateReducer = (state, changes) => {
    if (changes && changes.isOpen) {
      const { options } = this.state
      const highlightedIndex = options.findIndex(
        (i) => i.value === state.selectedItem.value,
      )
      return {
        ...changes,
        highlightedIndex,
      }
    }

    switch (changes.type) {
      case Downshift.stateChangeTypes.clickItem: {
        return {
          ...changes,
          isOpen: changes.isOpen,
          highlightedIndex: state.highlightedIndex,
        }
      }
      default: {
        return changes
      }
    }
  }

  // eslint-disable-next-line complexity
  renderDropdown = ({
    getInputProps,
    getToggleButtonProps,
    getItemProps,
    isOpen,
    highlightedIndex,
    selectedItem,
    openMenu,
    closeMenu,
    inputValue,
    itemToString,
    getMenuProps,
  }) => {
    const {
      extraClassNames: {
        containerClassNames,
        optionsClassNames,
        inputClassNames,
        errorClassNames,
      },
      className,
      placeholder,
      icon,
      iconName,
      disableInput,
      disabled,
      disableOptionIntl,
      openUpward,
      required,
      multiSelect,
      vehicleDisplayName,
      otherInputProps,
      sortItems,
      clearable,
      hint,
    } = this.props
    const { options } = this.state
    const isWithValue = selectedItem ? 'is-withValue' : ''
    const sortByProp = vehicleDisplayName ? vehicleDisplayName : 'name'
    this.openMenu = openMenu
    this.toggleMenu = isOpen ? closeMenu : openMenu

    const inputDisplayValue = isPlainObject(this.props.controlled)
      ? this.props.controlled.inputValue
      : isOpen && !multiSelect
        ? inputValue
        : multiSelect
          ? this.itemsToString(selectedItem)
          : itemToString(selectedItem)

    return (
      <div
        className={`InputDropdown ${containerClassNames || className} ${
          placeholder === ' ' ? 'InputDropdown--noPlaceholder' : ''
        }`}
      >
        <div className="InputDropdown-container">
          {disableInput ? (
            <button
              id={this.props.dropdownButtonId ? this.props.dropdownButtonId : undefined}
              className={`InputDropdown-button ${isWithValue} ${inputClassNames}`}
              {...getToggleButtonProps({ disabled })}
            >
              <div className={`InputDropdown-button-placeholder ${isWithValue}`}>
                {ctIntl.formatMessage({ id: placeholder })}
              </div>
              {inputDisplayValue}
            </button>
          ) : (
            <TextInput
              {...getInputProps({
                ...otherInputProps,
                placeholder,
                icon,
                iconName,
                disabled,
                disableAutofill: true,
                onFocus: this.handleInputFocus,
                onBlur: this.handleInputBlur,
                onChange: this.handleInputChange,
                onClick: this.handleClick,
                getRef: this.getRef,
                forceOriginalValue: isPlainObject(this.props.controlled),
                value: inputDisplayValue,
                autoComplete: 'off',
                extraClassNames: { inputClassNames, errorClassNames },
                hint,
              })}
            />
          )}
          {!disabled && (
            <>
              {clearable && !!selectedItem.value && isOpen && (
                <div className={`InputDropdown-clear-root ${!!iconName && 'withIcon'}`}>
                  <Icon
                    icon="times"
                    className="InputDropdown-clear"
                    onClick={this.clearInput}
                  />
                </div>
              )}
              {!icon && !iconName && (
                <Icon
                  icon="caret-down"
                  className={`InputDropdown-arrow ${isOpen ? 'is-open' : ''}`}
                />
              )}
            </>
          )}

          {disableInput && iconName && (
            <Icon
              icon={iconName}
              className={`TextInput-faIcon  ${
                isOpen ? 'TextInput-faIcon--focused' : ''
              }`}
            />
          )}

          {required && <span className="InputDropdown-required">*</span>}
          {isOpen && options.length > 0 && (
            <div
              {...getMenuProps()}
              className={`InputDropdown-options ${
                openUpward ? 'is-upward' : ''
              } ${optionsClassNames}`}
            >
              {(sortItems
                ? dropDownSort(options, sortByProp, selectedItem)
                : options
              ).map((item, index) =>
                this.renderDropdownItem({
                  item,
                  index,
                  sortByProp,
                  getItemProps,
                  highlightedIndex,
                  disableOptionIntl,
                  selectedItem,
                }),
              )}
            </div>
          )}
        </div>
      </div>
    )
  }

  renderDropdownItem = ({
    item,
    index,
    sortByProp,
    selectedItem,
    getItemProps,
    highlightedIndex,
    disableOptionIntl,
  }) => {
    const { multiSelect, activeOptionDataKey } = this.props
    const isSelected = (() => {
      if (multiSelect) {
        return item.value in selectedItem
      }

      if (activeOptionDataKey) {
        return item[activeOptionDataKey] === selectedItem[activeOptionDataKey]
      }
      return item.name === selectedItem[sortByProp]
    })()

    return item && item.name ? (
      <div
        key={item.key || item.value || item.name}
        {...getItemProps({
          item,
          index,
          className: `InputDropdown-options-option ${
            item.disabled
              ? 'is-disabled'
              : highlightedIndex === index
                ? 'is-focused'
                : ''
          } ${isSelected ? 'is-selected' : ''}`,
        })}
        {...(item.disabled ? { onClick: () => {} } : {})}
      >
        {/* Checkbox for multiselect only */}
        {this.props.multiSelect && (
          <Checkbox
            name="sure"
            value={isSelected}
            extraClassNames={{
              containerClassNames: 'InputDropdown-options-checkbox',
            }}
            small
          />
        )}
        {item.Component ? (
          <item.Component item={item} />
        ) : disableOptionIntl ? (
          item[sortByProp]
        ) : (
          ctIntl.formatMessage({ id: item.name })
        )}
      </div>
    ) : null
  }

  render() {
    return (
      <Downshift
        {...this.props}
        selectedItem={this.state.selectedItem || this.state.defaultSelectedItem}
        defaultSelectedItem={this.state.defaultSelectedItem}
        itemToString={this.props.itemToString || this.itemToString}
        onChange={this.handleDownshiftOnChange}
        onStateChange={this.props.onStateChange}
        defaultInputValue={this.props.defaultInputValue}
        stateReducer={this.stateReducer}
        defaultHighlightedIndex={0}
        scrollIntoView={(node) => {
          if (node && this.props.shouldMoveToView && !this.props.isOpen) {
            node.scrollIntoView({ block: 'center' })
          }
        }}
      >
        {this.renderDropdown}
      </Downshift>
    )
  }
}

InputDropdown.propTypes = {
  analytics: shape({
    eventCategory: string,
  }),
  dropdownButtonId: string,
  // Configuration
  disableFilter: bool,
  filterPredicate: func,
  itemSearchTransform: func,
  disableOptionIntl: bool,
  maxItems: number,
  disableInput: bool,
  disabled: bool,
  returnItem: bool,
  id: string,
  openUpward: bool,

  // Downshift
  activeOption: oneOfType([
    shape({
      name: string,
      Component: func,
    }),
    any,
  ]),
  activeOptionDataKey: string,
  controlled: shape({ inputValue: string.isRequired }),
  defaultInputValue: string,
  onStateChange: func,
  itemToString: func,

  // Text Input
  onInputChange: func,
  placeholder: oneOfType([element, func, string]),
  icon: element,
  iconName: string,

  // Dropdown
  options: arrayOf(
    shape({
      name: string,
      Component: func,
    }),
  ),
  onChange: func.isRequired,

  // Configuration
  extraClassNames: shape({
    containerClassNames: string,
    inputClassNames: string,
    errorClassNames: string,
    optionsClassNames: string,
  }),
  className: string,
  otherInputProps: shape({}),
  sortItems: bool,
  shouldMoveToView: bool,
  resetOptionsOnFocus: bool,
  clearable: bool,
  onElementFocus: func,
}

InputDropdown.defaultProps = {
  analytics: null,
  dropdownButtonId: '',
  controlled: null,
  disableFilter: false,
  openUpward: false,
  filterPredicate: null,
  itemSearchTransform: null,
  disableOptionIntl: false,
  maxItems: null,
  disableInput: false,
  disabled: false,
  itemToString: null,
  returnItem: false,
  activeOption: undefined,
  activeOptionDataKey: undefined,
  defaultInputValue: '',
  onStateChange: undefined,
  onInputChange: null,
  placeholder: ' ',
  icon: null,
  id: null,
  iconName: '',
  options: [],
  defaultSelectedIndex: null,
  className: '',
  extraClassNames: {
    containerClassNames: '',
    inputClassNames: '',
    errorClassNames: '',
    optionsClassNames: '',
  },
  otherInputProps: {},
  sortItems: true,
  shouldMoveToView: true,
  resetOptionsOnFocus: true,
  clearable: false,
  onElementFocus: undefined,
}

export default InputDropdown

function mapStateToProps(state, { options, input: { value: activeOption } }) {
  const isFunc = typeof options === 'function'
  return {
    options: isFunc ? options(state) : options,
    activeOption,
  }
}

const FormInputDropdown = (props) => (
  <InputDropdown
    {...props}
    {...props.input}
    {...(props.usePropsEvent ? { onChange: props.onChange } : {})}
    activeOption={props.value ? props.value : props.input.value}
  />
)

FormInputDropdown.propTypes = {
  ...InputDropdown.propTypes,
  input: shape({}).isRequired,
  onChange: func,
  usePropsEvent: bool,
  value: string,
}

FormInputDropdown.defaultProps = {
  ...InputDropdown.defaultProps,
  usePropsEvent: false,
  value: null,
}

export const InputConnectedDropdown = connect(mapStateToProps)(FormInputDropdown)
