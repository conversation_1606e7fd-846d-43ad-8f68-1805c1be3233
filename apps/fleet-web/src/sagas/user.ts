import { replace, push } from 'connected-react-router'
import { jwtDecode } from 'jwt-decode'

import * as Sen<PERSON> from '@sentry/browser'
import {
  takeLatest,
  call,
  put,
  fork,
  spawn,
  select,
  takeLeading,
  putResolve,
} from 'typed-redux-saga'

import { HubConnectionBuilder, LogLevel, type HubConnection } from '@microsoft/signalr'

import { captureLead } from '../temp-fixes/register-flow'
import userAPI from '../api/user'
import cache from '../api/cache'
import {
  makeLoadable,
  makeToast,
  makeMessage,
  waitForCondition,
  createLoadableSaga,
  waitForJwtAccessToken,
} from './utils'
import { fetchSharedVehicleDataSaga } from './vehicle-user-shared'
import {
  LOG_IN,
  FEDERATED_LOG_IN,
  TOKEN_LOG_IN,
  loginFailed,
  SUB_USER_LOGIN,
  receivedThirdPartyPreLoginData,
  SET_USER_PASSWORD,
  PASSWORD_UPDATE_STATUS,
  REGISTER,
  SAVE_PROFILE,
  ON_SAVE_PROFILE,
  SAVE_PREFERENCE,
  SET_SYSTEM_STATE_MESSAGE,
  UPDATE_USER_IMAGE,
  SUBMIT_HELP_REQUEST,
  federatedLogin,
  fetchPreLoginData,
  RECEIVE_USER_SETTINGS,
  TOGGLE_SSO_HASH,
  login,
  type tokenLogin,
  type subUserLogin,
  getThirdPartyUser,
  submittedThirdPartyLogin,
  type setUserPassword,
  type register,
  getSettings_UNSAFE,
  getLocale,
  type toggleUserSsoHash,
  type saveProfile,
  type savePreferences,
  type updateUserImage,
  type submitHelpRequest,
  LOGGED_OUT,
  tokenLoginSucceeded,
  loginSucceeded,
  getKarooUiTheme,
  logoutFailed,
  resetUserStateBeforeAttemptingALogin,
  loggedOutFromAnotherTab,
  logout,
  handleSaveProfileSuccess,
  receivedTwoFAPreLoginData,
  startedPreLoginFetch,
  failedPreLoginFetch,
  getPreLoginQuery,
  getEnableWelcomePageSetting,
  defaultPreferences,
  startLogOut,
  onUpdateVehicleLivePositionSuccess,
  getSettings,
  getUserStateFromSaga,
  type DevicePositionStreamSubscriptionMsg,
  receivePreLoginData,
  setKeyIdbStateWithReduxSync,
  getIdbStateForKey,
  setKeyIdbStateOnRedux,
} from 'duxs/user'
import { getAppMainUrl } from 'duxs/user-route-selectors'
import { getUser } from 'duxs/user'
import { ctIntl } from 'cartrack-ui-kit'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import { queryClient } from 'src/RouterRoot'
import type { LoginTabOptionValue } from 'src/modules/app/authentication/login'
import { isNil, isEmpty } from 'lodash'
import type { ParsedKarooUiCustomizableTheme } from 'api/user/utils'
import { match, P } from 'ts-pattern'
import { GA4 } from 'src/shared/google-analytics4'
import {
  isNilOrEmptyString,
  isNonEmptyTrimmedStringAndNotFalseish,
  secondsToMs,
} from 'cartrack-utils'
import { onLeftPanelResetAllVehicleFiltersButtonClick } from 'src/modules/map-view/actions'
import { idbStateKeysToDeleteOnLogout } from 'src/hooks/useUserIdbState'
import { batchReduxStateUpdatesFromSaga } from './actions'
import {
  onAppMounted,
  onUrlLoginSearchParamsChange,
  triggerRefreshJWTAccessToken,
} from 'duxs/sharedActions'
import { buildQueryStringFromObject } from 'api/utils'
import {
  getVehicles,
  TRY_REFETCH_VEHICLES_POSITIONS,
  type VehiclesReduxState,
  type RealTimeVehicleUpdateObject,
} from 'duxs/vehicles'
import type { AppSagaInjectedDeps } from './root-saga'
import type { ReadonlyDeep } from 'type-fest'
import { getMapStateFromSaga } from 'duxs/map'
import {
  getJwtAccessToken,
  getShowLivePositionsStoreless,
  getCanSetupSignalRHubConnectionMetaStoreless,
  getCanSetupSignalRHubConnectionMeta,
} from 'duxs/user-sensitive-selectors'
import { getReduxStore_DANGER } from 'src/ReduxStoreSyncProvider'
import type { TerminalSerial } from 'api/types'
import { AutoClearableCache } from 'src/util-functions/AutoClearableCache'
import { enqueueSnackbarWithButtonAction } from 'src/components/Snackbar/Notistack/utils'
import { SETTINGS } from 'src/modules/app/components/routes/settings'
import { update as updateIdb } from 'idb-keyval'

// Using auto clearable cache to prevent accidental memory leaks
const realTimeVehiclePositionsQueue = new AutoClearableCache<
  TerminalSerial,
  DevicePositionStreamSubscriptionMsg
>(400)
function runRealTimeVehiclePositionsQueueUpdates() {
  if (realTimeVehiclePositionsQueue.getSize() === 0) {
    return
  }
  const { dispatch } = getReduxStore_DANGER()

  dispatch(
    batchReduxStateUpdatesFromSaga({
      vehiclesState: (state): VehiclesReduxState => {
        const newVehicles = [...state.vehicles]
        for (let i = 0; i < newVehicles.length; i++) {
          const vehicle = newVehicles[i]
          if (realTimeVehiclePositionsQueue.getSize() === 0) {
            // No more messages to process. We can stop looping over remaining vehicles
            // This improves performance of these updates
            break
          }
          const msg = realTimeVehiclePositionsQueue.get(vehicle.terminalSerial)
          if (!msg) {
            continue
          }

          // Remove the message from the queue and process it
          realTimeVehiclePositionsQueue.delete(vehicle.terminalSerial)

          if (msg.eventType === 'NotSupported') {
            // NotSupported eventType usually contains faulty data. We can skip it
            continue
          }

          const updateObject: RealTimeVehicleUpdateObject = {
            latitude: msg.position.latitude,
            longitude: msg.position.longitude,
            bearing: msg.position.gpsHeading,
            speed: msg.position.speed,
            __webSocketEventTsUnix: new Date(msg.eventTsUtc).getTime(),
          }

          const updatedVehicle: typeof vehicle = {
            ...vehicle,
            ...updateObject,
          }

          // console.log('UPDATED_VEHICLE:', updatedVehicle.registration)
          newVehicles[i] = updatedVehicle
        }

        return {
          ...state,
          vehicles: newVehicles,
        }
      },
    }),
  )
}

let queueIntervalId: number | null = null

function* createSingletonDevicesPositionsStream({
  hubConnection,
  terminalSerials,
}: {
  hubConnection: ReadonlyDeep<HubConnection>
  terminalSerials: Array<TerminalSerial>
}) {
  const { hubConnectionMeta } = yield* select(getUserStateFromSaga)
  if (hubConnectionMeta?.devicePositionStreamSubscription) {
    // Dispose of the previous subscription if it exists
    hubConnectionMeta.devicePositionStreamSubscription.dispose()
  }
  // Make sure there is only one handler for this event to prevent race conditions
  const streamSubscription = hubConnection
    .stream<DevicePositionStreamSubscriptionMsg>('StreamDevicePosition', {
      Serials: terminalSerials,
    })
    .subscribe({
      next: (msgItem) => {
        // console.log('NEXT', msgItem)
        realTimeVehiclePositionsQueue.set(msgItem.serial, msgItem)
      },
      complete: () => {},
      error: () => {},
    })

  if (queueIntervalId) {
    window.clearInterval(queueIntervalId)
  }
  queueIntervalId = window.setInterval(runRealTimeVehiclePositionsQueueUpdates, 3000)

  return { streamSubscription }
}

/**
 * For some reason, the promise returned by hubConnection.start() is not callable from sagas.
 * The promise has probably been tempered with by the signalR library.
 * As such, we wrap it in a new "normal" Promise to make it callable from sagas using yield* call()
 */
const startHubConnection = (hubConnection: HubConnection) =>
  new Promise<void>((resolve, reject) =>
    hubConnection.start().then(resolve).catch(reject),
  )

function* maybeSetupUserHubConnection({
  initialSubscribedTerminalSerials,
  accessToken,
  userSettings,
}: {
  initialSubscribedTerminalSerials: Array<TerminalSerial> | 'not_available_yet'
  accessToken: string | null | undefined
  userSettings: Record<string, unknown>
}) {
  if (!isNonEmptyTrimmedStringAndNotFalseish(accessToken)) {
    // Protect against faulty BE implementation of jwt auth
    return
  }
  const setupMeta = getCanSetupSignalRHubConnectionMetaStoreless(userSettings)
  if (!setupMeta) {
    return
  }

  try {
    const hubConnection = new HubConnectionBuilder()
      .withUrl(setupMeta.connectionUrl, {
        accessTokenFactory: () => accessToken,
      })
      .withAutomaticReconnect()
      .configureLogging(
        ENV.NODE_ENV === 'development' ? LogLevel.Debug : LogLevel.Error,
      )
      .build()

    yield* call(startHubConnection, hubConnection)

    // Only start stream when connection is established
    const streamSubscription =
      initialSubscribedTerminalSerials === 'not_available_yet'
        ? null
        : (yield* createSingletonDevicesPositionsStream({
            hubConnection,
            terminalSerials: initialSubscribedTerminalSerials,
          })).streamSubscription

    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          hubConnectionMeta: {
            devicePositionStreamSubscription: streamSubscription,
            connection: hubConnection,
          },
        },
      }),
    )
  } catch (error) {
    // Do not throw error to prevent blocking the login process and showing toast to the user
    if (ENV.NODE_ENV === 'development') {
      return
    } else {
      Sentry.captureException(error)
    }
  }
}

function* fetchSystemStateMessage() {
  const systemStateMessage = yield* call(userAPI.fetchSystemStateMessage)
  if (
    systemStateMessage &&
    systemStateMessage.systemStateMessage[0] &&
    systemStateMessage.systemStateMessage[0].out_message
  ) {
    yield* put({
      type: SET_SYSTEM_STATE_MESSAGE,
      payload: {
        systemStateMessage: systemStateMessage.systemStateMessage[0].out_message,
      },
    })
    return true
  }

  return false
}

function* toggleSsoHashSaga(action: ReturnType<typeof toggleUserSsoHash>) {
  const ssoHash = yield* call(userAPI.toggleSSOHash, action.payload)
  yield* put({
    type: RECEIVE_USER_SETTINGS,
    payload: {
      settings: {
        ssoHash,
      },
    },
  })
}

function* maybeDisplayResolutionWarning() {
  const w = Math.max(document.documentElement.clientWidth, window.innerWidth || 0)
  if (w < 1024) {
    yield* call(
      makeMessage,
      ctIntl.formatMessage({ id: 'Window or screen size too small' }),
      ctIntl.formatMessage({
        id: 'We recommend a screen that supports 1024 x 768 pixels or greater for the best experience of Fleet.',
      }),
      ctIntl.formatMessage({ id: 'Got it' }),
    )
  }
}

function* loginSaga(
  { storeDispatch, history }: AppSagaInjectedDeps,
  {
    payload,
  }: Pick<ReturnType<typeof login> | ReturnType<typeof federatedLogin>, 'payload'>,
) {
  const userFromLocalStorage = localStorage.getItem('user')
  const user = yield* select(getUser)

  if (
    user ||
    (userFromLocalStorage !== null && !isEmpty(JSON.parse(userFromLocalStorage)))
  ) {
    // We are currently logged in on the current tab or in another tab
    yield logoutSaga({ payload: {} }) // Log out before trying to log in
  }

  // MAKE SURE we receive the pre-login data before logging in. Essential data like locales are needed for the login process
  yield waitForCondition(function* () {
    const preLoginQuery = yield* select(getPreLoginQuery)
    return preLoginQuery.status === 'success'
  })

  yield* put(resetUserStateBeforeAttemptingALogin(payload))

  const locale = yield* select(getLocale)

  try {
    const account =
      payload.type === 'federated'
        ? yield* call(userAPI.federatedLogin, payload.federatedResponse)
        : yield* call(
            userAPI.login,
            match(payload)
              .with({ type: 'sso' }, ({ sso, t }) => ({
                username: sso,
                subUserUsername: '',
                password: undefined,
                locale: '',
                otp: '',
                vehicle: payload.vehicle,
                ssoToken: t,
              }))
              .with(
                { type: 'credentials_login' },
                ({ password, username, adminLogin, subUsername }) => ({
                  username,
                  subUserUsername: adminLogin === 'admin' ? '' : subUsername || '',
                  password,
                  locale,
                  otp: undefined,
                  vehicle: payload.vehicle,
                }),
              )
              .with({ type: 'otp' }, ({ otp, username }) => ({
                username,
                subUserUsername: '',
                password: undefined,
                locale,
                otp,
                vehicle: payload.vehicle,
              }))
              .exhaustive(),
          )

    if (account.status === 'THIRD_PARTY_USER') {
      yield* put(
        receivedThirdPartyPreLoginData({
          vehicleList: account.vehicleList,
          loginPayloadData: payload,
        }),
      )

      yield* put(replace('/third-party-user'))
      return
    }

    if (account.status === 'TWO_FACTOR_AUTHENTICATION') {
      // save the 2FA contacts
      yield* put(
        receivedTwoFAPreLoginData({
          email: account.email,
          phone: account.phone,
          loginData: account.loginData,
        }),
      )

      // redirect to the 2FA url
      yield* put(push('/2fa'))
      return
    }

    if (account.status === 'FEDERATED_TWO_FACTOR_AUTHENTICATION') {
      // save the 2FA contacts
      yield* put(
        receivedTwoFAPreLoginData({
          email: account.email,
          phone: account.phone,
          loginData: {
            type: 'federated',
            ...account.loginData,
          },
        }),
      )

      // redirect to the 2FA url
      yield* put(push('/2fa'))
      return
    }

    if (account.status === 'SUCCEEDED') {
      const { user, timeZones, settings, diagnosticStatus, accessToken } = account

      let newRefreshTokenTimeoutId: number | null = null
      if (accessToken) {
        try {
          const { timeoutId } = yield* setupDelayedTaskForRefreshToken({
            accessToken,
            storeDispatch,
          })
          newRefreshTokenTimeoutId = timeoutId
        } catch (error) {
          Sentry.captureException(error)
          console.error('[Cartrack] - JWT login authentication failed', error)
          // Since JWT auth is still not the main way for authentication, we need to proceed like nothing happened in case something fails on accessToken is not defined (even though it should)
          newRefreshTokenTimeoutId = null
        }
      }

      // Pick up user's preferences from local storage
      let preferences = localStorage.getItem('userPreferences' + user.id)
      preferences = preferences ? JSON.parse(preferences) : null

      // Store data to persist over reloads
      const sUser = JSON.stringify(user)
      const sSettings = JSON.stringify(settings)
      const sTimeZones = JSON.stringify(timeZones)
      const sPreferences = JSON.stringify(preferences)
      const sDiagnosticStatus = JSON.stringify(diagnosticStatus)

      // Add to session whatever needs to be synced between opened browser tabs
      sessionStorage.setItem('diagnosticStatus', sDiagnosticStatus)
      sessionStorage.setItem('userTimezones', sTimeZones)
      sessionStorage.setItem('userSettings', sSettings)

      sessionStorage.setItem('userPreferences', sPreferences)

      // Save user settings and preferences regardless if 'Remember me' is on or off to save it across tabs
      localStorage.setItem('user', sUser)
      localStorage.setItem('diagnosticStatus', sDiagnosticStatus)
      localStorage.setItem('userTimezones', sTimeZones)
      localStorage.setItem('userSettings', sSettings)
      localStorage.setItem('userPreferences' + user.id, sPreferences)
      if (payload.type === 'credentials_login') {
        localStorage.setItem('loginSelectedTab', payload.adminLogin)
      }

      yield* put(
        batchReduxStateUpdatesFromSaga({
          userState: {
            // Make token available before calling tfms API
            jwtAccessToken: isNonEmptyTrimmedStringAndNotFalseish(accessToken)
              ? accessToken
              : null,
          },
          mapState: {
            zoom: settings.defaultMapZoom || 1,
          },
        }),
      )

      const hasSystemStateMessage = yield* call(fetchSystemStateMessage)
      if (!hasSystemStateMessage) yield* fork(maybeDisplayResolutionWarning)

      // Set user ID in Google Analytics
      GA4.setUserId(
        // Make sure we count sub users as different users from their parent
        isNilOrEmptyString(user.cuid) ? `${user.id}` : `${user.id}_${user.cuid}`,
      )
      GA4.event({
        category: 'User',
        action: 'User Logged In Successfully',
        value: Number(user.id),
      })

      yield* maybeSetupUserHubConnection({
        accessToken,
        userSettings: settings,
        initialSubscribedTerminalSerials: 'not_available_yet',
      })

      yield* put(
        loginSucceeded({
          loginMethodType: payload.type,
          apiData: account,
          preferences,
          debug: payload.type === 'otp',
          vehicleIdToSelectOnMap:
            'vehicleIdToSelectOnMap' in payload
              ? payload.vehicleIdToSelectOnMap
              : undefined,
          refreshTokenTimeoutId: newRefreshTokenTimeoutId,
        }),
      )

      if (account.status2FA === 'ACTIVATED_BUT_EMPTY') {
        enqueueSnackbarWithButtonAction({
          message: ctIntl.formatMessage({ id: 'login.2fa.activatedButEmpty' }),
          snackBarOptions: {
            variant: 'warning',
            persist: true,
            anchorOrigin: { horizontal: 'center', vertical: 'top' },
          },
          buttonText: ctIntl.formatMessage({ id: 'Add Contact' }),
          buttonAction: () => {
            history.replace(SETTINGS.subMenusRoutes.PROFILE_SETTINGS.path)
          },
        })
      }

      const appMainUrl = yield* select(getAppMainUrl)
      yield* put(replace(appMainUrl))
      return
    }

    yield* put(loginFailed(account))
  } catch (error) {
    GA4.event({
      category: 'User',
      action: 'User Login Failed',
    })
    yield* put(loginFailed({ unexpectedError: error.message }))

    throw error
  }
}

function* federatedLoginSaga(
  injectedDeps: AppSagaInjectedDeps,
  action: ReturnType<typeof federatedLogin>,
) {
  try {
    yield* call(loginSaga, injectedDeps, action)
  } catch (error) {
    GA4.event({
      category: 'User',
      action: 'Federated Login Failed',
    })
    yield* put(replace('/login'))

    throw error
  }
}

function* subUserLoginSaga(action: ReturnType<typeof subUserLogin>) {
  const { userId, name } = action.payload

  const account = yield* call(userAPI.subUserLogin, userId)

  yield* put(
    login({
      type: 'sso',
      sso: name,
      t: account,
    }),
  )
}

function* tokenLoginSaga({
  payload: { userId, vehicleId, token, clientId, getSharedVehicleData },
}: ReturnType<typeof tokenLogin>) {
  const currentUser = yield* select(getUser)

  /**
   * ? TODO: Fix redux persist not persisting full user object
   * [FABF-797] Fixes refresh on share link requiring login
   * This fix is a workaround, it however does not resolve the root cause
   * of the issue. Further investigation is required to understand why Redux persist
   * returns a user object with a single property from local/sessionStorage
   */
  if (
    currentUser &&
    Object.keys(currentUser).length > 1 &&
    !currentUser.isTokenAccount
  ) {
    const appMainUrl = yield* select(getAppMainUrl)
    yield* put(replace(appMainUrl))
    yield makeMessage(
      ctIntl.formatMessage({ id: 'Already Logged In' }),
      ctIntl.formatMessage({
        id: 'Please log out first to preview this shared vehicle link.',
      }),
      ctIntl.formatMessage({ id: 'Got it' }),
    )
    return
  }

  try {
    const { user, settings } = yield* call(userAPI.tokenLogin, {
      userId,
      vehicleId,
      token,
      clientId,
    })

    const jsonUser = JSON.stringify({ authToken: user.authToken })
    sessionStorage.setItem('user', jsonUser)

    yield* put(
      tokenLoginSucceeded({
        user,
        settings,
      }),
    )

    if (getSharedVehicleData) {
      yield* spawn(fetchSharedVehicleDataSaga, {
        type: '',
        payload: { vehicleId },
      })
    }
  } catch (error) {
    yield* put(replace('/login'))
    if (error.message === 'WRONG_CREDENTIALS') {
      throw new Error(
        ctIntl.formatMessage({
          id: 'login.token.invalid',
        }),
      )
    }
    throw error
  }
}

function* setUserPasswordSaga({
  payload: { currentPassword, password },
}: ReturnType<typeof setUserPassword>) {
  const response = yield* call(userAPI.setUserPassword, password, currentPassword)

  const isSuccess =
    response === 'Password update successful' ||
    response === 'Password changed. Please log in again'
  const shouldLogout = response === 'Password changed. Please log in again'

  yield* put({
    type: PASSWORD_UPDATE_STATUS,
    payload: {
      updatedTS: new Date(),
      isSuccess,
      response: isSuccess ? '' : response,
    },
  })
  if (isSuccess) yield* call(makeToast, 'success', response)
  if (shouldLogout) yield* putResolve(logout({}))
}

function* registerSaga(action: ReturnType<typeof register>) {
  const { DOTNumber, noDOT } = action.payload
  const newPayload: Record<string, FixMeAny> = {
    ...action.payload,
    DOTNumber: noDOT ? String(Math.round(Math.random() * 1e9)) : DOTNumber,
  }

  try {
    yield* call(captureLead, newPayload)
  } catch {
    // Nothing to do here
  }

  const message = yield* call(userAPI.register, newPayload)
  if (message === 'User created and linked to DOT Number.') {
    // do nothing
  } else {
    yield* call(makeToast, 'error', message)
  }
}

function* loggedOutFromAnotherTabSaga() {
  const user = yield* select(getUser)

  if (user?.isTokenAccount) {
    // ignore logged out from another tab for token accounts
    return
  }

  yield* put(logout({ meta: { logoutFromOtherOpenTabs: true } }))
}

function* logoutSaga(
  { payload }: Pick<ReturnType<typeof logout>, 'payload'>,
  retriesNum = 0,
): Generator<any, unknown, unknown> {
  const { meta = {} } = payload
  if (retriesNum > 2) {
    // Pointless to keep retrying a logout operation after a retry
    return
  }
  const { refreshTokenTimeoutId, hubConnectionMeta } =
    yield* select(getUserStateFromSaga)

  hubConnectionMeta?.connection.stop()

  yield* put(startLogOut())

  const saveLocale = localStorage.getItem('locale')
  const saveloginSelectedTab = localStorage.getItem('loginSelectedTab')

  // Use localStorage to communicate logout to other tabs if this was a manual event
  if (!meta.logoutFromOtherOpenTabs) {
    // calling backend logout only in current tab to prevent conflict of fn cookie
    try {
      yield* call(userAPI.backendLogout)
      localStorage.setItem('logoutFromOtherOpenTabs', Date.now() as FixMeAny)
    } catch {
      yield* put(logoutFailed())
      return yield logoutSaga({ payload }, retriesNum + 1)
    }
  }

  if (refreshTokenTimeoutId !== null) {
    // Clean up the refresh token when logout is successful
    window.clearTimeout(refreshTokenTimeoutId)
  }

  // Save existing user preferences
  const cachedUser = (JSON.parse as FixMeAny)(localStorage.getItem('user'))

  let cachedUserPreferencesFromReduxKey = null
  let cachedUserPreferencesFromRedux = null
  if (cachedUser) {
    cachedUserPreferencesFromReduxKey = 'userPreferences' + cachedUser.id
    cachedUserPreferencesFromRedux = localStorage.getItem(
      cachedUserPreferencesFromReduxKey,
    )
  }

  // Clear all persisted/stored data in the browser
  localStorage.clear()
  sessionStorage.clear()

  // API chache
  cache.clear()

  // Put back cached user preferences
  if (cachedUserPreferencesFromReduxKey && cachedUserPreferencesFromRedux) {
    localStorage.setItem(
      cachedUserPreferencesFromReduxKey,
      cachedUserPreferencesFromRedux,
    )
  }

  localStorage.setItem('locale', saveLocale as FixMeAny)
  localStorage.setItem('loginSelectedTab', saveloginSelectedTab as LoginTabOptionValue)

  for (const [, value] of idbStateKeysToDeleteOnLogout) {
    value.delete()
  }
  idbStateKeysToDeleteOnLogout.clear()

  //Clear all react query cache
  queryClient.removeQueries()

  yield* put({ type: LOGGED_OUT })
  if (!meta.preventRedirect) {
    yield* put(replace('/login'))
  }
  return
}

function* updateProfileSaga({ payload }: ReturnType<typeof saveProfile>) {
  const { newPassword: password, currentPassword } = payload.formData
  const userUpdatedPassword = password && currentPassword
  let passwordUpdateResponse = ''

  if (userUpdatedPassword) {
    passwordUpdateResponse = yield* call(
      userAPI.setUserPassword,
      password,
      currentPassword,
    )
  }

  const isSuccess =
    passwordUpdateResponse === 'Password update successful' ||
    passwordUpdateResponse === 'Password changed. Please log in again' ||
    !passwordUpdateResponse
  const shouldLogout =
    passwordUpdateResponse === 'Password changed. Please log in again'

  if (isSuccess && !shouldLogout) {
    try {
      const oldSettings = yield* select(getSettings_UNSAFE)
      const { datetimeFormat, costs, isSubUser } = oldSettings

      yield* call(
        userAPI.updateUserProfileSettings,
        { costs, isSubUser },
        {
          ...payload.formData,
          datetimeFormat,
        },
      )
      const newSettings = yield* call(userAPI.fetchUserSettings)
      const settings = { ...oldSettings, ...newSettings }
      sessionStorage.setItem('userSettings', JSON.stringify(settings))
      yield* put({ type: ON_SAVE_PROFILE, payload: { settings } })
      yield* call(makeToast, 'success', 'User update successful')
    } catch {
      yield* call(makeToast, 'error', 'User update failed')
    }
  } else if (isSuccess && shouldLogout) {
    yield* call(makeToast, 'error', passwordUpdateResponse)
    yield* putResolve(logout({}))
  } else {
    yield* call(makeToast, 'error', passwordUpdateResponse)
  }
}

function* updateUserPreferencesSaga({
  payload: { key, value },
}: ReturnType<typeof savePreferences>) {
  let preferences = yield* select((state: AppState) => state.user.preferences)
  const user = yield* select((state: AppState) => state.user.user)
  if (user) {
    preferences = Object.assign(preferences, { [key]: value })
    localStorage.setItem('userPreferences' + user.id, JSON.stringify(preferences))
    sessionStorage.setItem('userPreferences', JSON.stringify(preferences))
  }
}

function* resetLeftPanelVehicleFiltersPreferencesSaga() {
  const { ...preferences } = yield* select((state: AppState) => state.user.preferences)
  const user = yield* select((state: AppState) => state.user.user)
  if (user) {
    const keysToReset = ['vehicleIconColors', 'geofenceColors', 'poiColors'] as const

    for (const key of keysToReset) {
      preferences[key] = defaultPreferences[key]
    }

    const mapState = yield* select(getMapStateFromSaga)
    yield* put(
      batchReduxStateUpdatesFromSaga({
        mapState: {
          activeFilters: { ...mapState.activeFilters, vehicles: {}, drivers: {} },
          carpoolActiveFilters: {
            ...mapState.carpoolActiveFilters,
            carpool: {},
          },
        },
        userState: { preferences },
      }),
    )

    localStorage.setItem('userPreferences' + user.id, JSON.stringify(preferences))
    sessionStorage.setItem('userPreferences', JSON.stringify(preferences))
  }
}

function* updateUserImageSaga(action: ReturnType<typeof updateUserImage>) {
  yield* call(userAPI.updateProfileImage, action.payload.base64Image)
}

function* submitHelpRequestSaga(action: ReturnType<typeof submitHelpRequest>) {
  yield* call(userAPI.submitHelpRequest, action.payload.request)
  yield* call(makeToast, 'success', 'Message sent!')
}

export function* maybeConnectLoggedInUserToHubWhenTokenIsAvailableSaga() {
  const vehicles = yield* select(getVehicles)

  yield* waitForJwtAccessToken()

  // Token is now available
  const accessToken = yield* select(getJwtAccessToken)
  if (!accessToken) {
    return // extra check to make ts happy
  }

  yield* maybeSetupUserHubConnection({
    initialSubscribedTerminalSerials: vehicles.map((v) => v.terminalSerial),
    accessToken,
    userSettings: yield* select(getSettings),
  })
}

function* maybeFetchPreLoginData({
  payload: { history },
}: ReturnType<typeof fetchPreLoginData>) {
  const preLoginQuery = yield* select(getPreLoginQuery)
  const isAlreadyFetching = preLoginQuery.fetchStatus === 'fetching'
  if (!isAlreadyFetching) {
    try {
      yield* put(startedPreLoginFetch())

      const {
        ctCountries,
        languages,
        federatedLogins,
        settings: rawPreLoginSettings,
        countriesWebsites,
      } = yield* call(userAPI.fetchPreLoginData)

      // Check if is first time fetching prelogin data
      if (preLoginQuery.status === 'pending' && ENV.NODE_ENV === 'production') {
        // Init Sentry to log errors (only in production)
        Sentry.init({
          enabled: !rawPreLoginSettings.disableThirdPartyLogging,
          dsn: ENV.SENTRY_DSN,
          release: ENV.APP_VERSION,
          environment: ENV.DEPLOYMENT_ENV,
          ignoreErrors: [
            /** This message can safely be ignored since it doesn't affect behavior (https://stackoverflow.com/a/50387233/7915511) **/
            'ResizeObserver loop limit exceeded',
          ],
        })

        if (
          rawPreLoginSettings.disableThirdPartyLogging &&
          // https://developers.google.com/analytics/devguides/collection/gtagjs/user-opt-out
          // Disables Google Analytics if the user has opted out
          GA4.tagId
        ) {
          ;(window as any)[`ga-disable-${GA4.tagId}`] = true
        }
        // Initialize google analytics
        GA4.initialize()
      }

      const userAuthenticatedExists = !isNil(yield* select(getUser))
      const currentKarooUiTheme = yield* select(getKarooUiTheme)

      if (userAuthenticatedExists) {
        yield* fork(maybeConnectLoggedInUserToHubWhenTokenIsAvailableSaga)
      }

      const preLoginSettings = (() => {
        const { styleProperties, ...remainingPreLoginSettings } = rawPreLoginSettings

        // When fetchPreLoginData is called AND the user is already authenticated (can happen when we refresh the page while authenticated, for instance), we don't want to override the user theme
        const karooUiTheme: ParsedKarooUiCustomizableTheme =
          userAuthenticatedExists && currentKarooUiTheme !== undefined
            ? currentKarooUiTheme
            : styleProperties.karooUiTheme

        return {
          ...remainingPreLoginSettings,
          styleProperties: { ...styleProperties, karooUiTheme },
        }
      })()

      const currentSettings =
        JSON.parse(sessionStorage.getItem('userSettings') as FixMeAny) || {}

      sessionStorage.setItem(
        'userSettings',
        JSON.stringify({ ...currentSettings, ...preLoginSettings }),
      )

      const defaultLocale = languages.options.find((o) => o.default)?.value ?? 'en-ZA'
      const fromLocal = localStorage.getItem('locale')
      const locale =
        fromLocal && languages.options.some((o) => o.value === fromLocal) // verify if the local locale is included in the language list
          ? (fromLocal as (typeof languages.options)[number]['value'])
          : defaultLocale

      yield* put(
        receivePreLoginData({
          languageList: languages,
          styleProperties: preLoginSettings.styleProperties,
          locale,
          ctCountries,
          federatedLogins,
          countriesWebsites,
        }),
      )

      yield* put({
        type: RECEIVE_USER_SETTINGS,
        payload: { settings: preLoginSettings },
      })

      const enableWelcomePageSetting = yield* select(getEnableWelcomePageSetting)

      if (localStorage.getItem('userAccessedTheSiteAtLeastOnce') === null) {
        localStorage.setItem('userAccessedTheSiteAtLeastOnce', 'true')

        const { location } = history
        if (
          enableWelcomePageSetting &&
          !preLoginSettings.useFederationLoginOnly &&
          location.pathname === '/' &&
          location.search === ''
        ) {
          history.replace('/welcome')
        }
      }
    } catch {
      yield* put(failedPreLoginFetch())
    }
  }
}

function* submitThirdPartyLoginSaga({
  payload: { vehicle },
}: ReturnType<typeof submittedThirdPartyLogin>) {
  const user = yield* select(getThirdPartyUser)
  if (user === null) {
    return
  }

  const payload = {
    ...user.loginPayloadData,
    vehicle,
  }

  yield* put(login(payload))
}

function* handleSaveProfileSuccessSaga() {
  const oldSettings = yield* select(getSettings_UNSAFE)
  const newSettings = yield* call(userAPI.fetchUserSettings)
  const settings = { ...oldSettings, ...newSettings }
  sessionStorage.setItem('userSettings', JSON.stringify(settings))
  yield* put({ type: ON_SAVE_PROFILE, payload: { settings } })
}

function* onUrlLoginSearchParamsChangeSaga(
  injectedDeps: AppSagaInjectedDeps,
  { payload: payload_ }: ReturnType<typeof onUrlLoginSearchParamsChange>,
) {
  const loginPayload = match(payload_)
    .returnType<ReturnType<typeof login>['payload']>()
    .with({ otp: P.nonNullable }, (payload) => ({
      type: 'otp',
      otp: payload.otp,
      username: payload.account,
      vehicleIdToSelectOnMap: payload.ot_vehicle_id,
    }))
    .with({ sso: P.nonNullable }, (payload) => ({
      type: 'sso',
      sso: payload.sso,
      t: payload.t,
    }))
    .with({ wresult: P.nonNullable }, ({ wresult }) => ({
      type: 'federated',
      federatedResponse: buildQueryStringFromObject({
        wresult,
      }),
    }))
    .with({ code: P.nonNullable }, ({ code, session_state, state }) => ({
      type: 'federated',
      federatedResponse: buildQueryStringFromObject({
        code,
        state,
        session_state,
      }),
    }))
    .exhaustive()

  if (loginPayload.type === 'federated') {
    yield* put(federatedLogin(loginPayload))
    return
  }

  // This saga logs out first if needed
  yield loginSaga(injectedDeps, { payload: loginPayload })
}

function* setupDelayedTaskForRefreshToken({
  accessToken,
  storeDispatch,
}: {
  accessToken: string
  storeDispatch: AppSagaInjectedDeps['storeDispatch']
}) {
  const { refreshTokenTimeoutId } = yield* select(getUserStateFromSaga)
  if (refreshTokenTimeoutId !== null) {
    // Make sure we __always__ clear the previous timeout before setting a new one
    window.clearTimeout(refreshTokenTimeoutId)
  }

  const { exp: expUnixInSecond } = jwtDecode(accessToken)
  let timeoutId: number | null = null
  if (expUnixInSecond) {
    const expUnixInMs = expUnixInSecond * 1000
    const timeToExpireInMs = expUnixInMs - Date.now()
    if (timeToExpireInMs < 0) {
      throw new Error('JWT token expired')
    }

    // Subtract some seconds to make sure we refresh the token before it expires
    const timeoutTimeInMs = timeToExpireInMs - secondsToMs(20)
    timeoutId = window.setTimeout(() => {
      storeDispatch(triggerRefreshJWTAccessToken())
    }, timeoutTimeInMs)

    return { timeoutId: timeoutId }
  }

  return { timeoutId: null }
}

function* refreshJWTAccessTokenSaga({ storeDispatch }: AppSagaInjectedDeps) {
  const { refreshTokenTimeoutId } = yield* select(getUserStateFromSaga)
  try {
    if (refreshTokenTimeoutId) {
      // Clear the timeout so that it doesn't refresh the token while we're refreshing manually, for instance
      window.clearTimeout(refreshTokenTimeoutId)
    }

    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          refreshJwtMutation: { status: 'pending' },
        },
      }),
    )

    /** refresh_token is included on an https cookie that this endpoint reads from */
    const { accessToken } = yield* call(userAPI.refreshJwtToken)
    const { timeoutId } = yield* setupDelayedTaskForRefreshToken({
      accessToken,
      storeDispatch,
    })

    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          refreshJwtMutation: { status: 'success' },
          jwtAccessToken: accessToken,
          refreshTokenTimeoutId: timeoutId,
        },
      }),
    )
  } catch (error) {
    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          refreshJwtMutation: { status: 'error' },
        },
      }),
    )
    // Do not throw here. While JWT auth is not the main auth method, we can't rely on the refresh token to work
    // Silently fail
    console.error('[Cartrack] - JWT refresh failed', error)
    Sentry.captureException(error)
  }
}

function* onAppMountedSaga(injectedDeps: AppSagaInjectedDeps) {
  const user = yield* select(getUser)
  if (!user) {
    // No need to refresh token if user is not logged in
    return
  }
  // If the user refreshes the page for instance or opens it in a new tab, the token will be refreshed (if they are still logged in)
  yield refreshJWTAccessTokenSaga(injectedDeps)
}

function* onUpdateVehicleLivePositionSuccessSaga({
  payload,
}: ReturnType<typeof onUpdateVehicleLivePositionSuccess>) {
  const vehicles = yield* select(getVehicles)
  const userState = yield* select(getUserStateFromSaga)

  const newSettings = {
    ...userState.settings,
    privacyHideLocationsFromDay: payload.newPrivacyHideLocationsFromDay,
  }
  yield* put(
    batchReduxStateUpdatesFromSaga({
      userState: { settings: newSettings },
    }),
  )

  yield* put({ type: TRY_REFETCH_VEHICLES_POSITIONS })

  const showLivePositions = getShowLivePositionsStoreless(newSettings)
  if (!showLivePositions) {
    userState.hubConnectionMeta?.devicePositionStreamSubscription?.dispose()
    return
  }

  if (!userState.hubConnectionMeta) {
    // Should already have a connection but if for some reason it doesn't, we create a new one
    yield* maybeSetupUserHubConnection({
      accessToken: userState.jwtAccessToken,
      initialSubscribedTerminalSerials: vehicles.map((v) => v.terminalSerial),
      userSettings: newSettings,
    })
    return
  }

  const setupMeta = yield* select(getCanSetupSignalRHubConnectionMeta)
  if (!setupMeta) {
    return
  }
  const { streamSubscription } = yield* createSingletonDevicesPositionsStream({
    hubConnection: userState.hubConnectionMeta.connection,
    terminalSerials: vehicles.map((v) => v.terminalSerial),
  })

  yield* put(
    batchReduxStateUpdatesFromSaga({
      userState: {
        hubConnectionMeta: {
          ...userState.hubConnectionMeta,
          devicePositionStreamSubscription: streamSubscription,
        },
      },
    }),
  )
}

function* setKeyIdbStateWithReduxSyncSaga({
  payload: { key, store, setStateAction },
}: ReturnType<typeof setKeyIdbStateWithReduxSync>) {
  async function setValueOnIdb(newValue: unknown) {
    try {
      await updateIdb(key, () => newValue, store)
    } catch {
      return
    }
  }
  const currentValue = yield* select((state: AppState) => getIdbStateForKey(state, key))
  const newValue = setStateAction(currentValue)
  setValueOnIdb(newValue)
  yield* put(setKeyIdbStateOnRedux({ key, value: newValue }))
}

export default function* userSaga(injectedDeps: AppSagaInjectedDeps) {
  yield* takeLeading(LOG_IN, createLoadableSaga(loginSaga), injectedDeps)
  yield* takeLeading(
    FEDERATED_LOG_IN,
    createLoadableSaga(federatedLoginSaga),
    injectedDeps,
  )
  yield* takeLatest(submittedThirdPartyLogin, makeLoadable, submitThirdPartyLoginSaga)
  yield* takeLatest(TOKEN_LOG_IN, tokenLoginSaga)
  yield* takeLeading(SUB_USER_LOGIN, makeLoadable, subUserLoginSaga)
  yield* takeLatest(REGISTER, makeLoadable, registerSaga)
  yield* takeLeading(logout, makeLoadable, logoutSaga)
  yield* takeLeading(loggedOutFromAnotherTab, loggedOutFromAnotherTabSaga)
  yield* takeLatest(SAVE_PROFILE, updateProfileSaga)
  yield* takeLatest(SAVE_PREFERENCE, updateUserPreferencesSaga)
  yield* takeLatest(
    onLeftPanelResetAllVehicleFiltersButtonClick,
    resetLeftPanelVehicleFiltersPreferencesSaga,
  )
  yield* takeLatest(UPDATE_USER_IMAGE, updateUserImageSaga)
  yield* takeLatest(SUBMIT_HELP_REQUEST, submitHelpRequestSaga)
  yield* takeLeading(fetchPreLoginData, createLoadableSaga(maybeFetchPreLoginData))
  yield* takeLatest(TOGGLE_SSO_HASH, toggleSsoHashSaga)
  yield* takeLatest(SET_USER_PASSWORD, setUserPasswordSaga)
  yield* takeLatest(
    handleSaveProfileSuccess,
    makeLoadable,
    handleSaveProfileSuccessSaga,
  )
  yield* takeLatest(
    onUrlLoginSearchParamsChange,
    onUrlLoginSearchParamsChangeSaga,
    injectedDeps,
  )
  yield* takeLeading(onAppMounted, onAppMountedSaga, injectedDeps)
  yield* takeLeading(
    triggerRefreshJWTAccessToken,
    refreshJWTAccessTokenSaga,
    injectedDeps,
  )

  yield* takeLatest(
    onUpdateVehicleLivePositionSuccess,
    onUpdateVehicleLivePositionSuccessSaga,
  )
  yield* takeLatest(setKeyIdbStateWithReduxSync, setKeyIdbStateWithReduxSyncSaga)
}
